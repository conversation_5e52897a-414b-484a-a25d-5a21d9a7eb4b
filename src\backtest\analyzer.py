#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测分析模块

提供回测结果分析、性能指标计算、图表生成等功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple, Any
import json

from loguru import logger

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.font_manager import FontProperties
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    logger.warning("matplotlib未安装，无法生成图表")


class BacktestAnalyzer:
    """回测分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.results: Optional[Dict] = None
        self.daily_df: Optional[pd.DataFrame] = None
        self.trade_df: Optional[pd.DataFrame] = None
        
        # 设置中文字体（如果可用）
        if HAS_MATPLOTLIB:
            try:
                self.font = FontProperties(fname='C:/Windows/Fonts/simhei.ttf')
            except:
                self.font = None
                
        logger.info("回测分析器初始化完成")
        
    def load_results(self, results: Dict):
        """加载回测结果"""
        self.results = results
        
        # 转换为DataFrame
        if 'daily_results' in results and results['daily_results']:
            self.daily_df = pd.DataFrame(results['daily_results'])
            self.daily_df['date'] = pd.to_datetime(self.daily_df['date'])
            self.daily_df.set_index('date', inplace=True)
            
        if 'trades' in results and results['trades']:
            trade_data = []
            for trade in results['trades']:
                trade_dict = {
                    'datetime': trade.datetime,
                    'symbol': trade.symbol,
                    'direction': trade.direction.value,
                    'offset': trade.offset.value,
                    'price': trade.price,
                    'volume': trade.volume
                }
                trade_data.append(trade_dict)
                
            self.trade_df = pd.DataFrame(trade_data)
            self.trade_df['datetime'] = pd.to_datetime(self.trade_df['datetime'])
            
        logger.info("回测结果加载完成")
        
    def calculate_detailed_metrics(self) -> Dict:
        """计算详细性能指标"""
        if not self.results or not self.daily_df is not None:
            return {}
            
        # 基础信息
        basic_info = self.results.get('basic_info', {})
        
        # 资金曲线分析
        capitals = self.daily_df['capital'].values
        returns = self.daily_df['capital'].pct_change().dropna().values
        
        # 收益指标
        total_return = (capitals[-1] - capitals[0]) / capitals[0]
        trading_days = len(capitals)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1
        
        # 风险指标
        volatility = np.std(returns) * np.sqrt(252)
        downside_returns = returns[returns < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        
        # 风险调整收益指标
        risk_free_rate = 0.03  # 假设无风险利率3%
        excess_return = annual_return - risk_free_rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0
        sortino_ratio = excess_return / downside_volatility if downside_volatility > 0 else 0
        
        # 回撤分析
        drawdown_info = self._calculate_drawdown_metrics(capitals)
        
        # 交易分析
        trade_metrics = self._calculate_trade_metrics()
        
        # 月度收益分析
        monthly_returns = self._calculate_monthly_returns()
        
        # 胜率分析
        win_loss_metrics = self._calculate_win_loss_metrics()
        
        detailed_metrics = {
            'basic_info': basic_info,
            'return_metrics': {
                'total_return': total_return,
                'annual_return': annual_return,
                'monthly_return': annual_return / 12,
                'daily_return': total_return / trading_days,
            },
            'risk_metrics': {
                'volatility': volatility,
                'downside_volatility': downside_volatility,
                'max_drawdown': drawdown_info['max_drawdown'],
                'max_drawdown_duration': drawdown_info['max_drawdown_duration'],
                'var_95': np.percentile(returns, 5),  # 95% VaR
                'cvar_95': np.mean(returns[returns <= np.percentile(returns, 5)]),  # 95% CVaR
            },
            'risk_adjusted_metrics': {
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': annual_return / drawdown_info['max_drawdown'] if drawdown_info['max_drawdown'] > 0 else 0,
                'information_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0,
            },
            'trade_metrics': trade_metrics,
            'monthly_returns': monthly_returns,
            'win_loss_metrics': win_loss_metrics,
            'drawdown_info': drawdown_info
        }
        
        return detailed_metrics
        
    def _calculate_drawdown_metrics(self, capitals: np.ndarray) -> Dict:
        """计算回撤指标"""
        # 计算回撤序列
        peak = np.maximum.accumulate(capitals)
        drawdown = (peak - capitals) / peak
        
        # 最大回撤
        max_drawdown = np.max(drawdown)
        max_drawdown_idx = np.argmax(drawdown)
        
        # 最大回撤持续时间
        max_drawdown_duration = 0
        current_duration = 0
        
        for i, dd in enumerate(drawdown):
            if dd > 0:
                current_duration += 1
                max_drawdown_duration = max(max_drawdown_duration, current_duration)
            else:
                current_duration = 0
                
        # 回撤次数和平均回撤
        drawdown_periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(drawdown):
            if dd > 0 and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif dd == 0 and in_drawdown:
                in_drawdown = False
                drawdown_periods.append((start_idx, i-1))
                
        drawdown_count = len(drawdown_periods)
        avg_drawdown = np.mean(drawdown[drawdown > 0]) if np.any(drawdown > 0) else 0
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_date': self.daily_df.index[max_drawdown_idx] if hasattr(self, 'daily_df') and self.daily_df is not None else None,
            'max_drawdown_duration': max_drawdown_duration,
            'drawdown_count': drawdown_count,
            'avg_drawdown': avg_drawdown,
            'drawdown_series': drawdown
        }
        
    def _calculate_trade_metrics(self) -> Dict:
        """计算交易指标"""
        if self.trade_df is None or self.trade_df.empty:
            return {}
            
        # 交易次数统计
        total_trades = len(self.trade_df)
        
        # 按品种统计
        symbol_trades = self.trade_df['symbol'].value_counts().to_dict()
        
        # 按方向统计
        direction_trades = self.trade_df['direction'].value_counts().to_dict()
        
        # 交易频率分析
        if total_trades > 0:
            trading_days = (self.trade_df['datetime'].max() - self.trade_df['datetime'].min()).days
            avg_trades_per_day = total_trades / max(trading_days, 1)
        else:
            avg_trades_per_day = 0
            
        return {
            'total_trades': total_trades,
            'avg_trades_per_day': avg_trades_per_day,
            'symbol_distribution': symbol_trades,
            'direction_distribution': direction_trades
        }
        
    def _calculate_monthly_returns(self) -> Dict:
        """计算月度收益"""
        if self.daily_df is None:
            return {}
            
        # 按月分组计算收益
        monthly_data = self.daily_df.resample('M')['capital'].agg(['first', 'last'])
        monthly_returns = (monthly_data['last'] - monthly_data['first']) / monthly_data['first']
        
        return {
            'monthly_returns': monthly_returns.to_dict(),
            'best_month': monthly_returns.max(),
            'worst_month': monthly_returns.min(),
            'positive_months': (monthly_returns > 0).sum(),
            'negative_months': (monthly_returns < 0).sum(),
            'avg_monthly_return': monthly_returns.mean()
        }
        
    def _calculate_win_loss_metrics(self) -> Dict:
        """计算胜负指标"""
        if not self.results:
            return {}
            
        basic_info = self.results.get('basic_info', {})
        
        # 从基础信息获取胜率
        win_rate = basic_info.get('win_rate', 0)
        total_trades = basic_info.get('total_trades', 0)
        
        win_trades = int(total_trades * win_rate)
        lose_trades = total_trades - win_trades
        
        return {
            'win_rate': win_rate,
            'lose_rate': 1 - win_rate,
            'win_trades': win_trades,
            'lose_trades': lose_trades,
            'profit_factor': 0,  # 需要详细的盈亏数据计算
        }
        
    def generate_report(self, output_path: str = None) -> str:
        """生成分析报告"""
        if not self.results:
            raise ValueError("请先加载回测结果")
            
        # 计算详细指标
        metrics = self.calculate_detailed_metrics()
        
        # 生成报告内容
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("回测分析报告")
        report_lines.append("=" * 80)
        
        # 基础信息
        basic_info = metrics.get('basic_info', {})
        report_lines.append("\n【基础信息】")
        report_lines.append(f"回测期间: {basic_info.get('start_date')} ~ {basic_info.get('end_date')}")
        report_lines.append(f"初始资金: {basic_info.get('initial_capital', 0):,.2f}")
        report_lines.append(f"最终资金: {basic_info.get('final_capital', 0):,.2f}")
        report_lines.append(f"总交易次数: {basic_info.get('total_trades', 0)}")
        
        # 收益指标
        return_metrics = metrics.get('return_metrics', {})
        report_lines.append("\n【收益指标】")
        report_lines.append(f"总收益率: {return_metrics.get('total_return', 0):.2%}")
        report_lines.append(f"年化收益率: {return_metrics.get('annual_return', 0):.2%}")
        report_lines.append(f"月均收益率: {return_metrics.get('monthly_return', 0):.2%}")
        
        # 风险指标
        risk_metrics = metrics.get('risk_metrics', {})
        report_lines.append("\n【风险指标】")
        report_lines.append(f"年化波动率: {risk_metrics.get('volatility', 0):.2%}")
        report_lines.append(f"最大回撤: {risk_metrics.get('max_drawdown', 0):.2%}")
        report_lines.append(f"最大回撤持续天数: {risk_metrics.get('max_drawdown_duration', 0)}")
        report_lines.append(f"95% VaR: {risk_metrics.get('var_95', 0):.2%}")
        
        # 风险调整收益指标
        risk_adj_metrics = metrics.get('risk_adjusted_metrics', {})
        report_lines.append("\n【风险调整收益指标】")
        report_lines.append(f"夏普比率: {risk_adj_metrics.get('sharpe_ratio', 0):.4f}")
        report_lines.append(f"索提诺比率: {risk_adj_metrics.get('sortino_ratio', 0):.4f}")
        report_lines.append(f"卡玛比率: {risk_adj_metrics.get('calmar_ratio', 0):.4f}")
        
        # 交易指标
        trade_metrics = metrics.get('trade_metrics', {})
        report_lines.append("\n【交易指标】")
        report_lines.append(f"总交易次数: {trade_metrics.get('total_trades', 0)}")
        report_lines.append(f"日均交易次数: {trade_metrics.get('avg_trades_per_day', 0):.2f}")
        
        # 胜负指标
        win_loss_metrics = metrics.get('win_loss_metrics', {})
        report_lines.append("\n【胜负指标】")
        report_lines.append(f"胜率: {win_loss_metrics.get('win_rate', 0):.2%}")
        report_lines.append(f"盈利交易次数: {win_loss_metrics.get('win_trades', 0)}")
        report_lines.append(f"亏损交易次数: {win_loss_metrics.get('lose_trades', 0)}")
        
        report_lines.append("\n" + "=" * 80)
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"分析报告已保存到: {output_path}")
            
        return report_content
        
    def plot_performance(self, output_path: str = None, show: bool = True) -> Optional[str]:
        """绘制性能图表"""
        if not HAS_MATPLOTLIB:
            logger.warning("matplotlib未安装，无法生成图表")
            return None
            
        if self.daily_df is None:
            logger.warning("无每日数据，无法生成图表")
            return None
            
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('回测性能分析', fontsize=16, fontproperties=self.font)
        
        # 1. 资金曲线
        ax1 = axes[0, 0]
        ax1.plot(self.daily_df.index, self.daily_df['capital'], 'b-', linewidth=2)
        ax1.set_title('资金曲线', fontproperties=self.font)
        ax1.set_ylabel('资金', fontproperties=self.font)
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        
        # 2. 每日收益率
        ax2 = axes[0, 1]
        daily_returns = self.daily_df['capital'].pct_change().dropna()
        ax2.plot(daily_returns.index, daily_returns * 100, 'g-', alpha=0.7)
        ax2.set_title('每日收益率', fontproperties=self.font)
        ax2.set_ylabel('收益率 (%)', fontproperties=self.font)
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        
        # 3. 回撤曲线
        ax3 = axes[1, 0]
        capitals = self.daily_df['capital'].values
        peak = np.maximum.accumulate(capitals)
        drawdown = (peak - capitals) / peak * 100
        ax3.fill_between(self.daily_df.index, drawdown, 0, color='red', alpha=0.3)
        ax3.set_title('回撤曲线', fontproperties=self.font)
        ax3.set_ylabel('回撤 (%)', fontproperties=self.font)
        ax3.grid(True, alpha=0.3)
        
        # 4. 收益分布
        ax4 = axes[1, 1]
        ax4.hist(daily_returns * 100, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax4.set_title('收益率分布', fontproperties=self.font)
        ax4.set_xlabel('收益率 (%)', fontproperties=self.font)
        ax4.set_ylabel('频次', fontproperties=self.font)
        ax4.grid(True, alpha=0.3)
        ax4.axvline(x=0, color='r', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            logger.info(f"性能图表已保存到: {output_path}")
            
        if show:
            plt.show()
        else:
            plt.close()
            
        return output_path
        
    def export_results(self, output_path: str, format: str = 'json'):
        """导出结果"""
        if not self.results:
            raise ValueError("请先加载回测结果")
            
        # 计算详细指标
        detailed_metrics = self.calculate_detailed_metrics()
        
        if format.lower() == 'json':
            # 处理不能序列化的对象
            export_data = self._prepare_export_data(detailed_metrics)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
                
        elif format.lower() == 'excel':
            try:
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    # 每日数据
                    if self.daily_df is not None:
                        self.daily_df.to_excel(writer, sheet_name='每日数据')
                        
                    # 交易数据
                    if self.trade_df is not None:
                        self.trade_df.to_excel(writer, sheet_name='交易记录', index=False)
                        
                    # 性能指标
                    metrics_df = pd.DataFrame([detailed_metrics])
                    metrics_df.to_excel(writer, sheet_name='性能指标', index=False)
                    
            except ImportError:
                logger.error("openpyxl未安装，无法导出Excel文件")
                raise
                
        logger.info(f"结果已导出到: {output_path}")
        
    def _prepare_export_data(self, data: Any) -> Any:
        """准备导出数据（处理不能序列化的对象）"""
        if isinstance(data, dict):
            return {k: self._prepare_export_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._prepare_export_data(item) for item in data]
        elif isinstance(data, np.ndarray):
            return data.tolist()
        elif isinstance(data, (np.integer, np.floating)):
            return float(data)
        elif isinstance(data, (datetime, date)):
            return data.isoformat()
        elif pd.isna(data):
            return None
        else:
            return data
