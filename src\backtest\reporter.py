#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测报告生成器

提供HTML、PDF等格式的回测报告生成功能。
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from loguru import logger

try:
    from jinja2 import Template, Environment, FileSystemLoader
    HAS_JINJA2 = True
except ImportError:
    HAS_JINJA2 = False
    logger.warning("jinja2未安装，无法生成HTML报告")

try:
    import weasyprint
    HAS_WEASYPRINT = True
except ImportError:
    HAS_WEASYPRINT = False
    logger.warning("weasyprint未安装，无法生成PDF报告")


class BacktestReporter:
    """回测报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.template_dir = Path(__file__).parent / "templates"
        self.template_dir.mkdir(exist_ok=True)
        
        # 创建默认模板
        self._create_default_templates()
        
        if HAS_JINJA2:
            self.env = Environment(loader=FileSystemLoader(str(self.template_dir)))
            
        logger.info("回测报告生成器初始化完成")
        
    def _create_default_templates(self):
        """创建默认模板"""
        # HTML报告模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #666;
            margin-top: 10px;
            font-size: 1.1em;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-card.positive {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        .metric-card.negative {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        .metric-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        .metric-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 0;
        }
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive-value {
            color: #4CAF50;
            font-weight: bold;
        }
        .negative-value {
            color: #f44336;
            font-weight: bold;
        }
        .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #ddd;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.2em;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>回测分析报告</h1>
            <div class="subtitle">生成时间: {{ report_time }}</div>
        </div>

        <!-- 基础信息 -->
        <div class="section">
            <h2>基础信息</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>回测期间</h3>
                    <div class="value">{{ basic_info.start_date }} ~ {{ basic_info.end_date }}</div>
                </div>
                <div class="metric-card">
                    <h3>初始资金</h3>
                    <div class="value">{{ "%.0f"|format(basic_info.initial_capital) }}</div>
                </div>
                <div class="metric-card {% if basic_info.final_capital > basic_info.initial_capital %}positive{% else %}negative{% endif %}">
                    <h3>最终资金</h3>
                    <div class="value">{{ "%.0f"|format(basic_info.final_capital) }}</div>
                </div>
                <div class="metric-card">
                    <h3>总交易次数</h3>
                    <div class="value">{{ basic_info.total_trades }}</div>
                </div>
            </div>
        </div>

        <!-- 收益指标 -->
        <div class="section">
            <h2>收益指标</h2>
            <div class="metrics-grid">
                <div class="metric-card {% if return_metrics.total_return > 0 %}positive{% else %}negative{% endif %}">
                    <h3>总收益率</h3>
                    <div class="value">{{ "%.2f%%"|format(return_metrics.total_return * 100) }}</div>
                </div>
                <div class="metric-card {% if return_metrics.annual_return > 0 %}positive{% else %}negative{% endif %}">
                    <h3>年化收益率</h3>
                    <div class="value">{{ "%.2f%%"|format(return_metrics.annual_return * 100) }}</div>
                </div>
                <div class="metric-card">
                    <h3>月均收益率</h3>
                    <div class="value">{{ "%.2f%%"|format(return_metrics.monthly_return * 100) }}</div>
                </div>
            </div>
        </div>

        <!-- 风险指标 -->
        <div class="section">
            <h2>风险指标</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>年化波动率</h3>
                    <div class="value">{{ "%.2f%%"|format(risk_metrics.volatility * 100) }}</div>
                </div>
                <div class="metric-card negative">
                    <h3>最大回撤</h3>
                    <div class="value">{{ "%.2f%%"|format(risk_metrics.max_drawdown * 100) }}</div>
                </div>
                <div class="metric-card">
                    <h3>回撤持续天数</h3>
                    <div class="value">{{ risk_metrics.max_drawdown_duration }}</div>
                </div>
                <div class="metric-card">
                    <h3>95% VaR</h3>
                    <div class="value">{{ "%.2f%%"|format(risk_metrics.var_95 * 100) }}</div>
                </div>
            </div>
        </div>

        <!-- 风险调整收益指标 -->
        <div class="section">
            <h2>风险调整收益指标</h2>
            <div class="metrics-grid">
                <div class="metric-card {% if risk_adjusted_metrics.sharpe_ratio > 1 %}positive{% elif risk_adjusted_metrics.sharpe_ratio > 0 %}{% else %}negative{% endif %}">
                    <h3>夏普比率</h3>
                    <div class="value">{{ "%.4f"|format(risk_adjusted_metrics.sharpe_ratio) }}</div>
                </div>
                <div class="metric-card {% if risk_adjusted_metrics.sortino_ratio > 1 %}positive{% elif risk_adjusted_metrics.sortino_ratio > 0 %}{% else %}negative{% endif %}">
                    <h3>索提诺比率</h3>
                    <div class="value">{{ "%.4f"|format(risk_adjusted_metrics.sortino_ratio) }}</div>
                </div>
                <div class="metric-card {% if risk_adjusted_metrics.calmar_ratio > 1 %}positive{% elif risk_adjusted_metrics.calmar_ratio > 0 %}{% else %}negative{% endif %}">
                    <h3>卡玛比率</h3>
                    <div class="value">{{ "%.4f"|format(risk_adjusted_metrics.calmar_ratio) }}</div>
                </div>
            </div>
        </div>

        <!-- 交易统计 -->
        <div class="section">
            <h2>交易统计</h2>
            <div class="table-container">
                <table>
                    <tr>
                        <th>指标</th>
                        <th>数值</th>
                    </tr>
                    <tr>
                        <td>总交易次数</td>
                        <td>{{ trade_metrics.total_trades }}</td>
                    </tr>
                    <tr>
                        <td>日均交易次数</td>
                        <td>{{ "%.2f"|format(trade_metrics.avg_trades_per_day) }}</td>
                    </tr>
                    <tr>
                        <td>胜率</td>
                        <td class="{% if win_loss_metrics.win_rate > 0.5 %}positive-value{% else %}negative-value{% endif %}">
                            {{ "%.2f%%"|format(win_loss_metrics.win_rate * 100) }}
                        </td>
                    </tr>
                    <tr>
                        <td>盈利交易次数</td>
                        <td class="positive-value">{{ win_loss_metrics.win_trades }}</td>
                    </tr>
                    <tr>
                        <td>亏损交易次数</td>
                        <td class="negative-value">{{ win_loss_metrics.lose_trades }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 图表占位符 -->
        <div class="section">
            <h2>性能图表</h2>
            <div class="chart-placeholder">
                图表功能需要安装matplotlib库
            </div>
        </div>

        <div class="footer">
            <p>本报告由量化交易系统自动生成</p>
        </div>
    </div>
</body>
</html>
        """
        
        html_template_path = self.template_dir / "backtest_report.html"
        with open(html_template_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
            
    def generate_html_report(self, metrics: Dict, output_path: str) -> str:
        """生成HTML报告"""
        if not HAS_JINJA2:
            raise ImportError("需要安装jinja2库才能生成HTML报告")
            
        try:
            template = self.env.get_template("backtest_report.html")
            
            # 准备模板数据
            template_data = {
                'report_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'basic_info': metrics.get('basic_info', {}),
                'return_metrics': metrics.get('return_metrics', {}),
                'risk_metrics': metrics.get('risk_metrics', {}),
                'risk_adjusted_metrics': metrics.get('risk_adjusted_metrics', {}),
                'trade_metrics': metrics.get('trade_metrics', {}),
                'win_loss_metrics': metrics.get('win_loss_metrics', {})
            }
            
            # 渲染模板
            html_content = template.render(**template_data)
            
            # 保存HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.info(f"HTML报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成HTML报告失败: {e}")
            raise
            
    def generate_pdf_report(self, metrics: Dict, output_path: str) -> str:
        """生成PDF报告"""
        if not HAS_WEASYPRINT:
            raise ImportError("需要安装weasyprint库才能生成PDF报告")
            
        try:
            # 先生成HTML
            html_path = output_path.replace('.pdf', '.html')
            self.generate_html_report(metrics, html_path)
            
            # 转换为PDF
            weasyprint.HTML(filename=html_path).write_pdf(output_path)
            
            # 删除临时HTML文件
            os.remove(html_path)
            
            logger.info(f"PDF报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成PDF报告失败: {e}")
            raise
            
    def generate_summary_report(self, metrics: Dict) -> str:
        """生成摘要报告"""
        basic_info = metrics.get('basic_info', {})
        return_metrics = metrics.get('return_metrics', {})
        risk_metrics = metrics.get('risk_metrics', {})
        risk_adj_metrics = metrics.get('risk_adjusted_metrics', {})
        
        summary_lines = []
        summary_lines.append("=" * 60)
        summary_lines.append("回测结果摘要")
        summary_lines.append("=" * 60)
        
        # 基础信息
        summary_lines.append(f"回测期间: {basic_info.get('start_date')} ~ {basic_info.get('end_date')}")
        summary_lines.append(f"初始资金: {basic_info.get('initial_capital', 0):,.0f}")
        summary_lines.append(f"最终资金: {basic_info.get('final_capital', 0):,.0f}")
        
        # 关键指标
        summary_lines.append(f"总收益率: {return_metrics.get('total_return', 0):.2%}")
        summary_lines.append(f"年化收益率: {return_metrics.get('annual_return', 0):.2%}")
        summary_lines.append(f"最大回撤: {risk_metrics.get('max_drawdown', 0):.2%}")
        summary_lines.append(f"夏普比率: {risk_adj_metrics.get('sharpe_ratio', 0):.4f}")
        
        # 评级
        rating = self._calculate_strategy_rating(metrics)
        summary_lines.append(f"策略评级: {rating}")
        
        summary_lines.append("=" * 60)
        
        return "\n".join(summary_lines)
        
    def _calculate_strategy_rating(self, metrics: Dict) -> str:
        """计算策略评级"""
        return_metrics = metrics.get('return_metrics', {})
        risk_metrics = metrics.get('risk_metrics', {})
        risk_adj_metrics = metrics.get('risk_adjusted_metrics', {})
        
        # 评分标准
        score = 0
        
        # 年化收益率评分 (0-30分)
        annual_return = return_metrics.get('annual_return', 0)
        if annual_return > 0.3:
            score += 30
        elif annual_return > 0.2:
            score += 25
        elif annual_return > 0.15:
            score += 20
        elif annual_return > 0.1:
            score += 15
        elif annual_return > 0.05:
            score += 10
        elif annual_return > 0:
            score += 5
            
        # 最大回撤评分 (0-25分)
        max_drawdown = risk_metrics.get('max_drawdown', 1)
        if max_drawdown < 0.05:
            score += 25
        elif max_drawdown < 0.1:
            score += 20
        elif max_drawdown < 0.15:
            score += 15
        elif max_drawdown < 0.2:
            score += 10
        elif max_drawdown < 0.3:
            score += 5
            
        # 夏普比率评分 (0-25分)
        sharpe_ratio = risk_adj_metrics.get('sharpe_ratio', 0)
        if sharpe_ratio > 2:
            score += 25
        elif sharpe_ratio > 1.5:
            score += 20
        elif sharpe_ratio > 1:
            score += 15
        elif sharpe_ratio > 0.5:
            score += 10
        elif sharpe_ratio > 0:
            score += 5
            
        # 胜率评分 (0-20分)
        win_loss_metrics = metrics.get('win_loss_metrics', {})
        win_rate = win_loss_metrics.get('win_rate', 0)
        if win_rate > 0.7:
            score += 20
        elif win_rate > 0.6:
            score += 15
        elif win_rate > 0.5:
            score += 10
        elif win_rate > 0.4:
            score += 5
            
        # 评级映射
        if score >= 85:
            return "A+ (优秀)"
        elif score >= 75:
            return "A (良好)"
        elif score >= 65:
            return "B+ (中等偏上)"
        elif score >= 55:
            return "B (中等)"
        elif score >= 45:
            return "C+ (中等偏下)"
        elif score >= 35:
            return "C (较差)"
        else:
            return "D (很差)"
            
    def generate_comparison_report(self, results_list: List[Dict], 
                                 strategy_names: List[str], 
                                 output_path: str) -> str:
        """生成策略对比报告"""
        if len(results_list) != len(strategy_names):
            raise ValueError("结果数量与策略名称数量不匹配")
            
        # 创建对比表格
        comparison_data = []
        
        for i, (results, name) in enumerate(zip(results_list, strategy_names)):
            basic_info = results.get('basic_info', {})
            return_metrics = results.get('return_metrics', {})
            risk_metrics = results.get('risk_metrics', {})
            risk_adj_metrics = results.get('risk_adjusted_metrics', {})
            
            row = {
                '策略名称': name,
                '总收益率': f"{return_metrics.get('total_return', 0):.2%}",
                '年化收益率': f"{return_metrics.get('annual_return', 0):.2%}",
                '最大回撤': f"{risk_metrics.get('max_drawdown', 0):.2%}",
                '夏普比率': f"{risk_adj_metrics.get('sharpe_ratio', 0):.4f}",
                '索提诺比率': f"{risk_adj_metrics.get('sortino_ratio', 0):.4f}",
                '卡玛比率': f"{risk_adj_metrics.get('calmar_ratio', 0):.4f}",
                '总交易次数': basic_info.get('total_trades', 0),
                '评级': self._calculate_strategy_rating(results)
            }
            comparison_data.append(row)
            
        # 生成对比报告
        report_lines = []
        report_lines.append("=" * 100)
        report_lines.append("策略对比分析报告")
        report_lines.append("=" * 100)
        
        # 表格标题
        headers = list(comparison_data[0].keys())
        header_line = " | ".join(f"{h:^12}" for h in headers)
        report_lines.append(header_line)
        report_lines.append("-" * len(header_line))
        
        # 表格数据
        for row in comparison_data:
            data_line = " | ".join(f"{str(row[h]):^12}" for h in headers)
            report_lines.append(data_line)
            
        report_lines.append("=" * 100)
        
        # 最佳策略推荐
        best_return = max(comparison_data, key=lambda x: float(x['年化收益率'].rstrip('%')))
        best_sharpe = max(comparison_data, key=lambda x: float(x['夏普比率']))
        min_drawdown = min(comparison_data, key=lambda x: float(x['最大回撤'].rstrip('%')))
        
        report_lines.append("\n推荐策略:")
        report_lines.append(f"最高收益: {best_return['策略名称']} ({best_return['年化收益率']})")
        report_lines.append(f"最佳风险调整收益: {best_sharpe['策略名称']} (夏普比率: {best_sharpe['夏普比率']})")
        report_lines.append(f"最小回撤: {min_drawdown['策略名称']} ({min_drawdown['最大回撤']})")
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
            
        logger.info(f"对比报告已生成: {output_path}")
        return output_path
