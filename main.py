#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WtPy量化交易系统主程序

系统的主入口文件，负责启动和管理整个交易系统。
"""

import argparse
import signal
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.engine import MainEngine
from loguru import logger


class QuantSystem:
    """量化交易系统主类"""
    
    def __init__(self, config_path: str = None):
        """初始化系统"""
        self.main_engine = MainEngine(config_path)
        self._setup_signal_handlers()
        
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        logger.info(f"接收到信号 {signum}，正在关闭系统...")
        self.stop()
        sys.exit(0)
        
    def start(self):
        """启动系统"""
        try:
            logger.info("=" * 50)
            logger.info("WtPy量化交易系统启动中...")
            logger.info("=" * 50)
            
            # 启动主引擎
            self.main_engine.start()
            
            logger.info("系统启动完成，按 Ctrl+C 退出")
            
            # 保持主线程运行
            try:
                while self.main_engine.active:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("接收到键盘中断信号")
                
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            raise
            
    def stop(self):
        """停止系统"""
        try:
            logger.info("正在停止系统...")
            self.main_engine.stop()
            logger.info("系统已停止")
        except Exception as e:
            logger.error(f"系统停止失败: {e}")
            
    def restart(self):
        """重启系统"""
        logger.info("正在重启系统...")
        self.main_engine.restart()
        logger.info("系统重启完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WtPy量化交易系统")
    parser.add_argument(
        "--config", 
        type=str, 
        default=None,
        help="配置文件路径"
    )
    parser.add_argument(
        "--mode",
        type=str,
        choices=["live", "simulation", "backtest"],
        default="simulation",
        help="运行模式"
    )
    parser.add_argument(
        "--gui",
        action="store_true",
        help="启动GUI界面"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="调试模式"
    )
    
    args = parser.parse_args()
    
    # 根据参数启动不同模式
    if args.gui:
        # 启动GUI模式
        from src.gui.main_window import start_gui
        start_gui(args.config)
    elif args.mode == "backtest":
        # 启动回测模式
        from src.backtest.cli import start_backtest
        start_backtest(args.config)
    else:
        # 启动交易模式
        system = QuantSystem(args.config)
        system.start()


if __name__ == "__main__":
    main()
