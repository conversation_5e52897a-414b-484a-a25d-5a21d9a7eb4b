#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据管理器模块

统一管理数据获取、存储、缓存等功能，提供统一的数据接口。
"""

import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict

import pandas as pd
from loguru import logger

from ..core.base import BaseEngine, Event, EventType, TickData, BarData
from .miniqmt_api import MiniqmtApi
from .database import DatabaseManager


class DataManager(BaseEngine):
    """数据管理器"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化数据管理器"""
        super().__init__(main_engine, event_engine)
        
        # 数据源
        self.miniqmt_api = MiniqmtApi(main_engine, event_engine)
        self.database = DatabaseManager(main_engine, event_engine)
        
        # 数据缓存
        self._tick_cache: Dict[str, TickData] = {}
        self._bar_cache: Dict[str, Dict[str, List[BarData]]] = defaultdict(lambda: defaultdict(list))
        
        # 订阅管理
        self._subscriptions: Dict[str, Dict] = {}
        self._callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('data', {})
            
        # 缓存配置
        self.cache_config = self.config.get('cache', {})
        self.memory_cache_enabled = self.cache_config.get('memory', {}).get('enabled', True)
        self.cache_max_size = self.cache_config.get('memory', {}).get('max_size', '1GB')
        self.cache_ttl = self.cache_config.get('memory', {}).get('ttl', 3600)
        
        # 数据质量控制
        self.quality_config = self.config.get('quality_control', {})
        self.validation_enabled = self.quality_config.get('validation', {}).get('enabled', True)
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 注册事件处理器
        if event_engine:
            event_engine.register(EventType.TICK_DATA, self._on_tick_data)
            event_engine.register(EventType.KLINE_DATA, self._on_bar_data)
            
        logger.info("数据管理器初始化完成")
        
    def start(self):
        """启动数据管理器"""
        logger.info("启动数据管理器...")
        
        # 启动数据库
        self.database.start()
        
        # 启动数据源
        self.miniqmt_api.start()
        
        logger.info("数据管理器启动完成")
        
    def stop(self):
        """停止数据管理器"""
        logger.info("停止数据管理器...")
        
        # 停止数据源
        self.miniqmt_api.stop()
        
        # 停止数据库
        self.database.stop()
        
        logger.info("数据管理器已停止")
        
    def subscribe_tick(self, symbol: str, callback: Callable = None) -> bool:
        """订阅Tick数据"""
        with self._lock:
            # 添加回调函数
            if callback:
                self._callbacks[f"tick_{symbol}"].append(callback)
                
            # 如果已经订阅，直接返回
            if f"tick_{symbol}" in self._subscriptions:
                logger.info(f"Tick数据已订阅: {symbol}")
                return True
                
            # 订阅数据源
            success = self.miniqmt_api.subscribe_tick(symbol)
            if success:
                self._subscriptions[f"tick_{symbol}"] = {
                    'type': 'tick',
                    'symbol': symbol,
                    'callbacks': self._callbacks[f"tick_{symbol}"]
                }
                logger.info(f"订阅Tick数据成功: {symbol}")
            else:
                logger.error(f"订阅Tick数据失败: {symbol}")
                
            return success
            
    def subscribe_bar(self, symbol: str, interval: str = '1m', callback: Callable = None) -> bool:
        """订阅K线数据"""
        with self._lock:
            sub_key = f"bar_{symbol}_{interval}"
            
            # 添加回调函数
            if callback:
                self._callbacks[sub_key].append(callback)
                
            # 如果已经订阅，直接返回
            if sub_key in self._subscriptions:
                logger.info(f"K线数据已订阅: {symbol} {interval}")
                return True
                
            # 订阅数据源
            success = self.miniqmt_api.subscribe_bar(symbol, interval)
            if success:
                self._subscriptions[sub_key] = {
                    'type': 'bar',
                    'symbol': symbol,
                    'interval': interval,
                    'callbacks': self._callbacks[sub_key]
                }
                logger.info(f"订阅K线数据成功: {symbol} {interval}")
            else:
                logger.error(f"订阅K线数据失败: {symbol} {interval}")
                
            return success
            
    def unsubscribe_tick(self, symbol: str, callback: Callable = None):
        """取消订阅Tick数据"""
        with self._lock:
            sub_key = f"tick_{symbol}"
            
            # 移除回调函数
            if callback and sub_key in self._callbacks:
                if callback in self._callbacks[sub_key]:
                    self._callbacks[sub_key].remove(callback)
                    
                # 如果还有其他回调，不取消订阅
                if self._callbacks[sub_key]:
                    return
                    
            # 取消订阅
            if sub_key in self._subscriptions:
                self.miniqmt_api.unsubscribe(symbol)
                del self._subscriptions[sub_key]
                if sub_key in self._callbacks:
                    del self._callbacks[sub_key]
                logger.info(f"取消订阅Tick数据: {symbol}")
                
    def unsubscribe_bar(self, symbol: str, interval: str = '1m', callback: Callable = None):
        """取消订阅K线数据"""
        with self._lock:
            sub_key = f"bar_{symbol}_{interval}"
            
            # 移除回调函数
            if callback and sub_key in self._callbacks:
                if callback in self._callbacks[sub_key]:
                    self._callbacks[sub_key].remove(callback)
                    
                # 如果还有其他回调，不取消订阅
                if self._callbacks[sub_key]:
                    return
                    
            # 取消订阅
            if sub_key in self._subscriptions:
                self.miniqmt_api.unsubscribe(symbol, interval)
                del self._subscriptions[sub_key]
                if sub_key in self._callbacks:
                    del self._callbacks[sub_key]
                logger.info(f"取消订阅K线数据: {symbol} {interval}")
                
    def get_tick_data(self, symbol: str) -> Optional[TickData]:
        """获取最新Tick数据"""
        with self._lock:
            # 先从缓存获取
            if symbol in self._tick_cache:
                return self._tick_cache[symbol]
                
            # 从数据源获取
            return self.miniqmt_api.get_tick_data(symbol)
            
    def get_bar_data(self, symbol: str, interval: str = '1d',
                     start: datetime = None, end: datetime = None,
                     count: int = None) -> List[BarData]:
        """获取K线数据"""
        with self._lock:
            # 如果指定了数量，优先从缓存获取最新数据
            if count and not start and not end:
                cache_data = self._bar_cache.get(symbol, {}).get(interval, [])
                if len(cache_data) >= count:
                    return cache_data[-count:]
                    
            # 从数据库查询
            exchange = symbol.split('.')[1] if '.' in symbol else ''
            db_data = self.database.get_bar_data(
                symbol=symbol,
                exchange=exchange,
                interval=interval,
                start=start,
                end=end,
                limit=count
            )
            
            if db_data:
                return db_data
                
            # 从数据源获取
            api_data = self.miniqmt_api.get_bar_data(symbol, interval)
            if api_data:
                # 根据条件过滤数据
                filtered_data = api_data
                if start:
                    filtered_data = [bar for bar in filtered_data if bar.datetime >= start]
                if end:
                    filtered_data = [bar for bar in filtered_data if bar.datetime <= end]
                if count:
                    filtered_data = filtered_data[-count:]
                    
                return filtered_data
                
            return []
            
    def get_history_data(self, symbol: str, interval: str = '1d',
                        start_time: str = '', end_time: str = '',
                        count: int = -1) -> Optional[pd.DataFrame]:
        """获取历史数据"""
        return self.miniqmt_api.get_history_data(symbol, interval, start_time, end_time, count)
        
    def download_history_data(self, symbol: str, interval: str = '1d',
                             start_time: str = '', end_time: str = '') -> bool:
        """下载历史数据"""
        return self.miniqmt_api.download_history_data(symbol, interval, start_time, end_time)
        
    def get_instrument_info(self, symbol: str) -> Optional[Dict]:
        """获取合约信息"""
        return self.miniqmt_api.get_instrument_info(symbol)
        
    def _on_tick_data(self, event: Event):
        """处理Tick数据事件"""
        tick_data: TickData = event.data
        if not tick_data:
            return
            
        with self._lock:
            # 数据验证
            if self.validation_enabled:
                if not self._validate_tick_data(tick_data):
                    logger.warning(f"Tick数据验证失败: {tick_data.symbol}")
                    return
                    
            # 更新缓存
            if self.memory_cache_enabled:
                self._tick_cache[tick_data.symbol] = tick_data
                
            # 保存到数据库
            self.database.save_tick_data(tick_data)
            
            # 调用回调函数
            sub_key = f"tick_{tick_data.symbol}"
            if sub_key in self._callbacks:
                for callback in self._callbacks[sub_key]:
                    try:
                        callback(tick_data)
                    except Exception as e:
                        logger.error(f"Tick数据回调异常: {e}")
                        
    def _on_bar_data(self, event: Event):
        """处理K线数据事件"""
        bar_data: BarData = event.data
        if not bar_data:
            return
            
        with self._lock:
            # 数据验证
            if self.validation_enabled:
                if not self._validate_bar_data(bar_data):
                    logger.warning(f"K线数据验证失败: {bar_data.symbol}")
                    return
                    
            # 更新缓存
            if self.memory_cache_enabled:
                symbol = bar_data.symbol
                interval = bar_data.interval
                
                # 添加到缓存
                self._bar_cache[symbol][interval].append(bar_data)
                
                # 限制缓存大小（保留最新1000条）
                if len(self._bar_cache[symbol][interval]) > 1000:
                    self._bar_cache[symbol][interval] = self._bar_cache[symbol][interval][-1000:]
                    
            # 保存到数据库
            self.database.save_bar_data(bar_data)
            
            # 调用回调函数
            sub_key = f"bar_{bar_data.symbol}_{bar_data.interval}"
            if sub_key in self._callbacks:
                for callback in self._callbacks[sub_key]:
                    try:
                        callback(bar_data)
                    except Exception as e:
                        logger.error(f"K线数据回调异常: {e}")
                        
    def _validate_tick_data(self, tick_data: TickData) -> bool:
        """验证Tick数据"""
        try:
            # 检查必要字段
            if not tick_data.symbol or not tick_data.exchange:
                return False
                
            # 检查价格合理性
            if tick_data.last_price <= 0:
                return False
                
            # 检查时间合理性
            if not tick_data.datetime:
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Tick数据验证异常: {e}")
            return False
            
    def _validate_bar_data(self, bar_data: BarData) -> bool:
        """验证K线数据"""
        try:
            # 检查必要字段
            if not bar_data.symbol or not bar_data.exchange:
                return False
                
            # 检查价格合理性
            if (bar_data.open_price <= 0 or bar_data.high_price <= 0 or
                bar_data.low_price <= 0 or bar_data.close_price <= 0):
                return False
                
            # 检查价格逻辑
            if (bar_data.high_price < bar_data.low_price or
                bar_data.high_price < bar_data.open_price or
                bar_data.high_price < bar_data.close_price or
                bar_data.low_price > bar_data.open_price or
                bar_data.low_price > bar_data.close_price):
                return False
                
            # 检查时间合理性
            if not bar_data.datetime:
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"K线数据验证异常: {e}")
            return False
            
    def get_subscriptions(self) -> Dict[str, Dict]:
        """获取订阅列表"""
        with self._lock:
            return self._subscriptions.copy()
            
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self._tick_cache.clear()
            self._bar_cache.clear()
            logger.info("数据缓存已清空")
            
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return self.miniqmt_api.active and self.database.active
