#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略模块演示脚本

演示如何使用策略框架开发和管理策略。
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.core.base import BarData
from src.strategy.manager import StrategyManager
from src.strategy.indicators import SMA, EMA, RSI, MACD, IndicatorManager
from loguru import logger

# 导入示例策略
sys.path.insert(0, str(Path(__file__).parent.parent / "strategies"))
from ma_cross_strategy import MovingAverageCrossStrategy


def create_sample_bar_data(symbol: str, base_price: float, count: int) -> list:
    """创建示例K线数据"""
    bars = []
    current_time = datetime.now()
    
    for i in range(count):
        # 模拟价格波动
        import random
        price_change = random.uniform(-0.02, 0.02)  # ±2%波动
        close_price = base_price * (1 + price_change)
        
        # 生成OHLC
        open_price = base_price
        high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.01))
        low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.01))
        
        bar = BarData(
            symbol=symbol,
            exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
            datetime=current_time + timedelta(minutes=i),
            interval="1m",
            open_price=round(open_price, 2),
            high_price=round(high_price, 2),
            low_price=round(low_price, 2),
            close_price=round(close_price, 2),
            volume=random.randint(1000, 10000),
            turnover=round(close_price * random.randint(1000, 10000), 2)
        )
        
        bars.append(bar)
        base_price = close_price  # 下一根K线的基准价格
        
    return bars


def demo_indicators():
    """演示技术指标"""
    logger.info("=" * 30)
    logger.info("技术指标演示")
    logger.info("=" * 30)
    
    # 创建指标
    sma5 = SMA(5)
    sma20 = SMA(20)
    ema12 = EMA(12)
    rsi = RSI(14)
    macd = MACD(12, 26, 9)
    
    # 创建指标管理器
    indicator_manager = IndicatorManager()
    indicator_manager.add_indicator("SMA5", sma5)
    indicator_manager.add_indicator("SMA20", sma20)
    indicator_manager.add_indicator("EMA12", ema12)
    indicator_manager.add_indicator("RSI", rsi)
    indicator_manager.add_indicator("MACD", macd)
    
    # 生成测试数据
    base_price = 10.0
    prices = []
    
    logger.info("生成价格数据并计算指标...")
    
    for i in range(50):
        # 模拟价格
        import random
        price_change = random.uniform(-0.01, 0.01)
        price = base_price * (1 + price_change)
        prices.append(price)
        base_price = price
        
        # 更新指标
        results = indicator_manager.update_all(price=price)
        
        # 显示部分结果
        if i >= 25 and i % 5 == 0:  # 从第26个数据开始，每5个显示一次
            logger.info(f"第{i+1}个数据点 价格: {price:.2f}")
            
            for name, value in results.items():
                if name == "MACD":
                    macd_line, signal_line, histogram = value
                    logger.info(f"  {name}: MACD={macd_line:.4f}, Signal={signal_line:.4f}, Hist={histogram:.4f}")
                else:
                    logger.info(f"  {name}: {value:.4f}")
                    
    logger.info("技术指标演示完成")


def demo_strategy_framework():
    """演示策略框架"""
    logger.info("\n" + "=" * 30)
    logger.info("策略框架演示")
    logger.info("=" * 30)
    
    # 创建主引擎
    main_engine = MainEngine()
    main_engine.start()
    
    try:
        # 创建策略管理器
        strategy_manager = StrategyManager(main_engine, main_engine.event_engine)
        strategy_manager.start()
        
        # 注册策略类
        strategy_manager.register_strategy_class(MovingAverageCrossStrategy)
        
        logger.info("已注册策略类:")
        for class_name in strategy_manager.get_strategy_classes():
            logger.info(f"  {class_name}")
            
        # 创建策略实例
        symbols = ["000001.SZ", "600000.SH"]
        parameters = {
            "short_period": 5,
            "long_period": 20,
            "ma_type": "SMA",
            "position_size": 1000,
            "stop_loss_pct": 0.05,
            "take_profit_pct": 0.1
        }
        
        success = strategy_manager.create_strategy(
            "MovingAverageCrossStrategy",
            "MA_Strategy_1",
            symbols,
            parameters
        )
        
        if success:
            logger.info("策略创建成功: MA_Strategy_1")
            
            # 获取策略信息
            info = strategy_manager.get_strategy_info("MA_Strategy_1")
            logger.info("策略信息:")
            logger.info(f"  名称: {info['name']}")
            logger.info(f"  类名: {info['class_name']}")
            logger.info(f"  品种: {info['symbols']}")
            logger.info(f"  参数: {info['parameters']}")
            
            # 启动策略
            success = strategy_manager.start_strategy("MA_Strategy_1")
            if success:
                logger.info("策略启动成功")
                
                # 获取策略实例
                strategy = strategy_manager.get_strategy("MA_Strategy_1")
                
                # 模拟市场数据
                logger.info("开始模拟市场数据...")
                
                for symbol in symbols:
                    bars = create_sample_bar_data(symbol, 10.0, 30)
                    
                    logger.info(f"为 {symbol} 生成了 {len(bars)} 根K线")
                    
                    # 逐个发送K线数据给策略
                    for i, bar in enumerate(bars):
                        strategy.on_bar(bar)
                        
                        # 每10根K线显示一次策略状态
                        if (i + 1) % 10 == 0:
                            positions = strategy.get_current_positions()
                            indicators = strategy.get_indicator_values(symbol)
                            
                            logger.info(f"{symbol} 第{i+1}根K线后状态:")
                            logger.info(f"  价格: {bar.close_price:.2f}")
                            logger.info(f"  持仓: {positions[symbol]['position']}")
                            logger.info(f"  短期MA: {indicators.get('short_ma', 0):.2f}")
                            logger.info(f"  长期MA: {indicators.get('long_ma', 0):.2f}")
                            logger.info(f"  信号: {indicators.get('last_signal', 0)}")
                            
                        time.sleep(0.01)  # 模拟时间间隔
                        
                # 显示策略统计
                stats = strategy.get_statistics()
                logger.info("\n策略统计:")
                logger.info(f"  总交易次数: {stats['total_trades']}")
                logger.info(f"  盈利交易: {stats['win_trades']}")
                logger.info(f"  亏损交易: {stats['lose_trades']}")
                logger.info(f"  胜率: {stats['win_rate']:.2%}")
                logger.info(f"  总盈亏: {stats['total_pnl']:.2f}")
                
                # 暂停和恢复交易
                logger.info("\n测试暂停和恢复交易...")
                strategy_manager.pause_strategy_trading("MA_Strategy_1")
                logger.info("交易已暂停")
                
                time.sleep(1)
                
                strategy_manager.resume_strategy_trading("MA_Strategy_1")
                logger.info("交易已恢复")
                
                # 更新策略参数
                logger.info("\n测试参数更新...")
                new_params = {"short_period": 10, "long_period": 30}
                success = strategy_manager.update_strategy_parameters("MA_Strategy_1", new_params)
                if success:
                    logger.info("参数更新成功")
                    updated_info = strategy_manager.get_strategy_info("MA_Strategy_1")
                    logger.info(f"新参数: {updated_info['parameters']}")
                    
                # 停止策略
                strategy_manager.stop_strategy("MA_Strategy_1")
                logger.info("策略已停止")
                
            else:
                logger.error("策略启动失败")
                
        else:
            logger.error("策略创建失败")
            
        # 显示所有策略信息
        all_strategies = strategy_manager.get_all_strategies_info()
        logger.info(f"\n当前策略数量: {len(all_strategies)}")
        
        for strategy_info in all_strategies:
            logger.info(f"策略: {strategy_info['name']} - 状态: {'运行中' if strategy_info['active'] else '已停止'}")
            
    except Exception as e:
        logger.error(f"策略框架演示异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理资源
        if 'strategy_manager' in locals():
            strategy_manager.stop()
        main_engine.stop()
        
    logger.info("策略框架演示完成")


def demo_multiple_strategies():
    """演示多策略管理"""
    logger.info("\n" + "=" * 30)
    logger.info("多策略管理演示")
    logger.info("=" * 30)
    
    # 创建主引擎
    main_engine = MainEngine()
    main_engine.start()
    
    try:
        # 创建策略管理器
        strategy_manager = StrategyManager(main_engine, main_engine.event_engine)
        strategy_manager.start()
        
        # 注册策略类
        strategy_manager.register_strategy_class(MovingAverageCrossStrategy)
        
        # 创建多个策略实例
        strategies_config = [
            {
                "name": "MA_5_20",
                "symbols": ["000001.SZ"],
                "params": {"short_period": 5, "long_period": 20, "position_size": 1000}
            },
            {
                "name": "MA_10_30", 
                "symbols": ["600000.SH"],
                "params": {"short_period": 10, "long_period": 30, "position_size": 1500}
            },
            {
                "name": "MA_EMA",
                "symbols": ["000300.SH"],
                "params": {"short_period": 12, "long_period": 26, "ma_type": "EMA", "position_size": 2000}
            }
        ]
        
        # 创建策略
        for config in strategies_config:
            success = strategy_manager.create_strategy(
                "MovingAverageCrossStrategy",
                config["name"],
                config["symbols"],
                config["params"]
            )
            
            if success:
                logger.info(f"创建策略成功: {config['name']}")
            else:
                logger.error(f"创建策略失败: {config['name']}")
                
        # 启动所有策略
        started_count = strategy_manager.start_all_strategies()
        logger.info(f"启动策略数量: {started_count}")
        
        # 显示策略状态
        all_strategies = strategy_manager.get_all_strategies_info()
        logger.info("\n当前策略状态:")
        for info in all_strategies:
            logger.info(f"  {info['name']}: {'运行中' if info['active'] else '已停止'} - "
                       f"品种: {info['symbols']} - 交易: {'是' if info['trading'] else '否'}")
                       
        # 模拟运行一段时间
        logger.info("\n模拟运行...")
        time.sleep(2)
        
        # 停止所有策略
        stopped_count = strategy_manager.stop_all_strategies()
        logger.info(f"停止策略数量: {stopped_count}")
        
    except Exception as e:
        logger.error(f"多策略管理演示异常: {e}")
        
    finally:
        # 清理资源
        if 'strategy_manager' in locals():
            strategy_manager.stop()
        main_engine.stop()
        
    logger.info("多策略管理演示完成")


def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("策略模块演示开始")
    logger.info("=" * 50)
    
    try:
        # 1. 技术指标演示
        demo_indicators()
        
        # 2. 策略框架演示
        demo_strategy_framework()
        
        # 3. 多策略管理演示
        demo_multiple_strategies()
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
        
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        import traceback
        traceback.print_exc()
        
    logger.info("=" * 50)
    logger.info("策略模块演示结束")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
