#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易模块测试

测试交易下单、撤单、持仓管理、风险控制等功能。
"""

import sys
import unittest
from pathlib import Path
from datetime import datetime, date
import time

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.core.base import Direction, Offset, OrderType, OrderStatus
from src.trading.trader import Trader
from src.trading.portfolio import Portfolio
from src.trading.risk_manager import RiskManager, RiskLevel


class TestTrader(unittest.TestCase):
    """交易器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        self.trader = Trader(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'trader'):
            self.trader.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.trader)
        self.assertEqual(self.trader.trading_mode, 'simulation')
        
    def test_start_stop(self):
        """测试启动停止"""
        self.trader.start()
        self.assertTrue(self.trader.active)
        
        # 检查账户初始化
        accounts = self.trader.get_accounts()
        self.assertGreater(len(accounts), 0)
        
        account = accounts[0]
        self.assertEqual(account.accountid, 'SIM_001')
        self.assertEqual(account.balance, 1000000.0)
        
        self.trader.stop()
        
    def test_submit_order(self):
        """测试提交订单"""
        self.trader.start()
        
        # 提交买入订单
        order_id = self.trader.submit_order(
            symbol="000001.SZ",
            exchange="SZ",
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=10.50,
            volume=1000,
            order_type=OrderType.LIMIT
        )
        
        self.assertIsNotNone(order_id)
        
        # 检查订单
        order = self.trader.get_order(order_id)
        self.assertIsNotNone(order)
        self.assertEqual(order.symbol, "000001.SZ")
        self.assertEqual(order.direction, Direction.LONG)
        self.assertEqual(order.volume, 1000)
        self.assertEqual(order.price, 10.50)
        
        # 等待模拟成交
        time.sleep(0.2)
        
        # 检查订单状态
        order = self.trader.get_order(order_id)
        self.assertIn(order.status, [OrderStatus.FILLED, OrderStatus.SUBMITTED])
        
    def test_cancel_order(self):
        """测试撤销订单"""
        self.trader.start()
        
        # 提交订单
        order_id = self.trader.submit_order(
            symbol="000001.SZ",
            exchange="SZ", 
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=10.50,
            volume=1000
        )
        
        # 立即撤销
        success = self.trader.cancel_order(order_id)
        self.assertTrue(success)
        
        # 检查订单状态
        order = self.trader.get_order(order_id)
        self.assertEqual(order.status, OrderStatus.CANCELLED)
        
    def test_get_orders(self):
        """测试获取订单列表"""
        self.trader.start()
        
        # 提交多个订单
        symbols = ["000001.SZ", "600000.SH"]
        order_ids = []
        
        for symbol in symbols:
            order_id = self.trader.submit_order(
                symbol=symbol,
                exchange=symbol.split('.')[1],
                direction=Direction.LONG,
                offset=Offset.OPEN,
                price=10.50,
                volume=1000
            )
            order_ids.append(order_id)
            
        # 获取所有订单
        all_orders = self.trader.get_orders()
        self.assertEqual(len(all_orders), 2)
        
        # 按品种获取订单
        sz_orders = self.trader.get_orders(symbol="000001.SZ")
        self.assertEqual(len(sz_orders), 1)
        self.assertEqual(sz_orders[0].symbol, "000001.SZ")


class TestPortfolio(unittest.TestCase):
    """投资组合测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        self.portfolio = Portfolio(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'portfolio'):
            self.portfolio.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.portfolio)
        self.assertEqual(self.portfolio.initial_capital, 1000000.0)
        self.assertEqual(self.portfolio.current_capital, 1000000.0)
        
    def test_start_stop(self):
        """测试启动停止"""
        self.portfolio.start()
        self.assertTrue(self.portfolio.active)
        
        # 检查初始化
        today = date.today()
        daily_balance = self.portfolio.get_daily_balance()
        self.assertIn(today, daily_balance)
        self.assertEqual(daily_balance[today], 1000000.0)
        
        self.portfolio.stop()
        
    def test_get_statistics(self):
        """测试获取统计信息"""
        self.portfolio.start()
        
        stats = self.portfolio.get_statistics()
        
        # 检查统计信息
        self.assertEqual(stats['initial_capital'], 1000000.0)
        self.assertEqual(stats['current_capital'], 1000000.0)
        self.assertEqual(stats['total_pnl'], 0.0)
        self.assertEqual(stats['total_trades'], 0)
        self.assertEqual(stats['win_rate'], 0.0)
        self.assertEqual(stats['position_count'], 0)


class TestRiskManager(unittest.TestCase):
    """风险管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        self.risk_manager = RiskManager(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'risk_manager'):
            self.risk_manager.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.risk_manager)
        self.assertEqual(self.risk_manager.max_position_ratio, 0.95)
        self.assertEqual(self.risk_manager.max_daily_loss, 0.05)
        
    def test_start_stop(self):
        """测试启动停止"""
        self.risk_manager.start()
        self.assertTrue(self.risk_manager.active)
        self.assertEqual(self.risk_manager.get_risk_level(), RiskLevel.LOW)
        
        self.risk_manager.stop()
        
    def test_check_order_risk(self):
        """测试订单风险检查"""
        from src.core.base import OrderData, AccountData
        
        self.risk_manager.start()
        
        # 创建测试订单
        order = OrderData(
            orderid="TEST_001",
            symbol="000001.SZ",
            exchange="SZ",
            type=OrderType.LIMIT,
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=10.50,
            volume=1000
        )
        
        # 创建测试账户
        account = AccountData(
            accountid="TEST_ACCOUNT",
            balance=1000000.0,
            available=1000000.0
        )
        
        # 检查风险
        passed, message = self.risk_manager.check_order_risk(order, account)
        self.assertTrue(passed)
        self.assertEqual(message, "风险检查通过")
        
        # 测试超大订单
        large_order = OrderData(
            orderid="TEST_002",
            symbol="000001.SZ",
            exchange="SZ",
            type=OrderType.LIMIT,
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=10.50,
            volume=100000  # 超过限额
        )
        
        passed, message = self.risk_manager.check_order_risk(large_order, account)
        self.assertFalse(passed)
        self.assertIn("超过限额", message)
        
    def test_risk_level_update(self):
        """测试风险等级更新"""
        from src.core.base import AccountData, PositionData
        
        self.risk_manager.start()
        
        # 创建高风险账户
        high_risk_account = AccountData(
            accountid="HIGH_RISK",
            balance=100000.0,
            available=10000.0  # 资金使用率90%
        )
        
        # 更新风险等级
        self.risk_manager.update_risk_level(high_risk_account)
        
        # 检查风险等级
        risk_level = self.risk_manager.get_risk_level()
        self.assertIn(risk_level, [RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL])
        
    def test_daily_trades_limit(self):
        """测试每日交易次数限制"""
        from src.core.base import OrderData
        
        self.risk_manager.start()
        
        # 模拟大量交易
        for i in range(150):  # 超过限制
            order = OrderData(
                orderid=f"TEST_{i:03d}",
                symbol="000001.SZ",
                exchange="SZ",
                type=OrderType.LIMIT,
                direction=Direction.LONG,
                offset=Offset.OPEN,
                price=10.50,
                volume=100
            )
            
            passed, message = self.risk_manager.check_order_risk(order)
            
            if i >= 100:  # 超过每日限制
                self.assertFalse(passed)
                self.assertIn("交易次数超限", message)
                break
            else:
                # 模拟订单提交
                from src.core.base import Event, EventType
                event = Event(type=EventType.ORDER_SUBMIT, data=order)
                self.risk_manager._on_order_submit(event)


class TestTradingIntegration(unittest.TestCase):
    """交易模块集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        
        self.trader = Trader(self.main_engine, self.main_engine.event_engine)
        self.portfolio = Portfolio(self.main_engine, self.main_engine.event_engine)
        self.risk_manager = RiskManager(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'risk_manager'):
            self.risk_manager.stop()
        if hasattr(self, 'portfolio'):
            self.portfolio.stop()
        if hasattr(self, 'trader'):
            self.trader.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_complete_trading_flow(self):
        """测试完整交易流程"""
        # 启动所有模块
        self.trader.start()
        self.portfolio.start()
        self.risk_manager.start()
        
        # 提交订单
        order_id = self.trader.submit_order(
            symbol="000001.SZ",
            exchange="SZ",
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=10.50,
            volume=1000
        )
        
        self.assertIsNotNone(order_id)
        
        # 等待成交
        time.sleep(0.3)
        
        # 检查订单状态
        order = self.trader.get_order(order_id)
        if order.status == OrderStatus.FILLED:
            # 检查成交记录
            trades = self.trader.get_trades()
            self.assertGreater(len(trades), 0)
            
            # 检查持仓
            positions = self.trader.get_positions()
            self.assertGreater(len(positions), 0)
            
            # 检查投资组合统计
            stats = self.portfolio.get_statistics()
            self.assertGreater(stats['total_trades'], 0)
            
        # 检查风险状态
        risk_level = self.risk_manager.get_risk_level()
        self.assertIsInstance(risk_level, RiskLevel)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
