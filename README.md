# 基于wtpy框架的量化交易系统

## 项目简介

这是一个基于wtpy框架开发的完整量化交易系统，集成了实盘交易、回测引擎、GUI管理界面等核心功能。系统采用模块化设计，支持多品种、多账户交易管理，提供丰富的策略开发工具和风险控制机制。

## 核心功能

### 1. 实盘交易模块
- 集成miniqmt接口实现实时行情数据订阅
- 实现交易下单、撤单、查询持仓等功能
- 支持多品种、多账户交易管理
- 实现风险控制和资金管理

### 2. 回测引擎
- 支持历史数据回测验证策略效果
- 提供详细的回测报告和性能指标
- 支持多种回测模式（逐笔、逐K线等）
- 实现滑点、手续费等真实交易成本模拟

### 3. GUI管理界面
- 策略管理：创建、编辑、启停策略
- 实时监控：持仓、盈亏、交易记录展示
- 回测分析：回测结果可视化展示
- 系统配置：参数设置、账户管理

## 技术架构

### 框架基础
- 基于wtpy框架开发，保持与wtpy生态的兼容性
- 遵循wtpy的最佳实践和设计模式
- 支持Python 3.8+

### 数据层设计
- 集成miniqmt实时数据接口
- 设计本地数据库存储历史行情数据
- 提供统一的数据接口，支持多数据源切换
- 实现数据缓存和增量更新机制

### 策略框架
- 提供标准化的策略开发模板
- 支持策略热插拔和动态加载
- 实现策略间的隔离和资源管理
- 提供丰富的技术指标和工具函数

## 项目结构

```
策略5/
├── README.md                   # 项目说明文档
├── requirements.txt            # 项目依赖
├── setup.py                   # 安装脚本
├── config/                    # 配置文件目录
│   ├── system_config.yaml     # 系统配置
│   ├── trading_config.yaml    # 交易配置
│   └── data_config.yaml       # 数据配置
├── src/                       # 源代码目录
│   ├── __init__.py
│   ├── core/                  # 核心模块
│   │   ├── __init__.py
│   │   ├── engine.py          # 主引擎
│   │   ├── event_engine.py    # 事件引擎
│   │   └── base.py           # 基础类定义
│   ├── data/                  # 数据模块
│   │   ├── __init__.py
│   │   ├── data_manager.py    # 数据管理器
│   │   ├── miniqmt_api.py     # miniqmt接口
│   │   └── database.py        # 数据库操作
│   ├── trading/               # 交易模块
│   │   ├── __init__.py
│   │   ├── trader.py          # 交易器
│   │   ├── portfolio.py       # 投资组合
│   │   └── risk_manager.py    # 风险管理
│   ├── backtest/              # 回测模块
│   │   ├── __init__.py
│   │   ├── backtest_engine.py # 回测引擎
│   │   ├── analyzer.py        # 分析器
│   │   └── reporter.py        # 报告生成
│   ├── strategy/              # 策略模块
│   │   ├── __init__.py
│   │   ├── base_strategy.py   # 策略基类
│   │   ├── template.py        # 策略模板
│   │   └── indicators.py      # 技术指标
│   └── gui/                   # GUI模块
│       ├── __init__.py
│       ├── main_window.py     # 主窗口
│       ├── widgets/           # 界面组件
│       └── resources/         # 资源文件
├── strategies/                # 策略示例
│   ├── __init__.py
│   ├── ma_strategy.py         # 移动平均策略
│   ├── bollinger_strategy.py  # 布林带策略
│   └── arbitrage_strategy.py  # 套利策略
├── data/                      # 数据存储
│   ├── market_data/           # 行情数据
│   ├── backtest_results/      # 回测结果
│   └── logs/                  # 日志文件
├── tests/                     # 测试代码
│   ├── __init__.py
│   ├── test_data.py
│   ├── test_trading.py
│   └── test_backtest.py
└── docs/                      # 文档
    ├── api_reference.md       # API参考
    ├── strategy_guide.md      # 策略开发指南
    ├── deployment.md          # 部署指南
    └── user_manual.md         # 用户手册
```

## 快速开始

### 环境要求
- Python 3.8+
- wtpy >= 0.9.9
- xtquant (miniqmt接口)
- PyQt5/PySide2 (GUI界面)

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行系统
```bash
python main.py
```

## 开发计划

- [x] 项目架构设计与环境搭建
- [ ] 数据接口层开发
- [ ] 核心交易引擎开发
- [ ] 回测引擎开发
- [ ] 策略框架开发
- [ ] 风险控制与资金管理
- [ ] GUI管理界面开发
- [ ] 示例策略与文档编写
- [ ] 测试与优化

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
