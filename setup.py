#!/usr/bin/env python
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open('README.md', 'r', encoding='utf-8') as f:
        return f.read()

# 读取requirements文件
def read_requirements():
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="wtpy-quant-system",
    version="1.0.0",
    author="Quant Team",
    author_email="<EMAIL>",
    description="基于wtpy框架的完整量化交易系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/wtpy-quant-system",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Information Analysis",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "pytest-cov>=2.12.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
            "mypy>=0.910",
        ],
        "gui": [
            "PyQt5>=5.15.0",
            "pyqtgraph>=0.12.0",
        ],
        "ml": [
            "scikit-learn>=1.0.0",
            "tensorflow>=2.6.0",
            "torch>=1.9.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "wtpy-quant=src.main:main",
            "wtpy-backtest=src.backtest.cli:main",
            "wtpy-gui=src.gui.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.json", "*.ini", "*.txt"],
        "src.gui.resources": ["*.png", "*.jpg", "*.ico", "*.qss"],
    },
    zip_safe=False,
    keywords="quantitative trading wtpy backtest strategy",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/wtpy-quant-system/issues",
        "Source": "https://github.com/your-repo/wtpy-quant-system",
        "Documentation": "https://your-docs-site.com",
    },
)
