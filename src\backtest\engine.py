#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测引擎模块

提供历史数据回测功能，支持多种回测模式和性能分析。
"""

import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict
import numpy as np

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, TickData, BarData, OrderData, TradeData, 
    PositionData, AccountData, Direction, Offset, OrderStatus, OrderType
)
from ..trading.trader import Trader
from ..trading.portfolio import Portfolio
from ..trading.risk_manager import RiskManager


class BacktestEngine(BaseEngine):
    """回测引擎"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化回测引擎"""
        super().__init__(main_engine, event_engine)
        
        # 回测配置
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('backtest', {})
            
        # 回测参数
        self.start_date: Optional[date] = None
        self.end_date: Optional[date] = None
        self.initial_capital = self.config.get('initial_capital', 1000000.0)
        self.commission_rate = self.config.get('commission_rate', 0.0003)
        self.slippage_rate = self.config.get('slippage_rate', 0.0001)
        
        # 回测模式
        self.mode = self.config.get('mode', 'bar')  # 'tick' or 'bar'
        self.frequency = self.config.get('frequency', '1d')  # K线周期
        
        # 数据管理
        self.data_manager = None
        self.historical_data: Dict[str, pd.DataFrame] = {}
        self.current_datetime: Optional[datetime] = None
        self.data_cursor: Dict[str, int] = defaultdict(int)
        
        # 交易模块
        self.trader: Optional[Trader] = None
        self.portfolio: Optional[Portfolio] = None
        self.risk_manager: Optional[RiskManager] = None
        
        # 策略管理
        self.strategies: Dict[str, Any] = {}
        self.strategy_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # 回测状态
        self.is_running = False
        self.is_paused = False
        self.current_progress = 0.0
        
        # 性能统计
        self.daily_results: List[Dict] = []
        self.trade_results: List[Dict] = []
        
        logger.info("回测引擎初始化完成")
        
    def start(self):
        """启动回测引擎"""
        logger.info("启动回测引擎...")
        
        # 初始化交易模块
        if not self.trader:
            self.trader = Trader(self.main_engine, self.event_engine)
            self.trader._simulation_mode = True
            
        if not self.portfolio:
            self.portfolio = Portfolio(self.main_engine, self.event_engine)
            
        if not self.risk_manager:
            self.risk_manager = RiskManager(self.main_engine, self.event_engine)
            
        # 启动交易模块
        self.trader.start()
        self.portfolio.start()
        self.risk_manager.start()
        
        logger.info("回测引擎启动完成")
        
    def stop(self):
        """停止回测引擎"""
        logger.info("停止回测引擎...")
        
        self.is_running = False
        
        # 停止交易模块
        if self.risk_manager:
            self.risk_manager.stop()
        if self.portfolio:
            self.portfolio.stop()
        if self.trader:
            self.trader.stop()
            
        logger.info("回测引擎已停止")
        
    def set_parameters(self, start_date: date, end_date: date, 
                      initial_capital: float = None, 
                      commission_rate: float = None,
                      slippage_rate: float = None):
        """设置回测参数"""
        self.start_date = start_date
        self.end_date = end_date
        
        if initial_capital is not None:
            self.initial_capital = initial_capital
            
        if commission_rate is not None:
            self.commission_rate = commission_rate
            
        if slippage_rate is not None:
            self.slippage_rate = slippage_rate
            
        logger.info(f"回测参数设置: {start_date} ~ {end_date}, "
                   f"资金: {self.initial_capital:,.0f}, "
                   f"手续费: {self.commission_rate:.4f}, "
                   f"滑点: {self.slippage_rate:.4f}")
                   
    def load_data(self, symbols: List[str], data_manager=None):
        """加载历史数据"""
        logger.info(f"加载历史数据: {symbols}")
        
        if data_manager:
            self.data_manager = data_manager
            
        if not self.start_date or not self.end_date:
            raise ValueError("请先设置回测时间范围")
            
        # 清空现有数据
        self.historical_data.clear()
        self.data_cursor.clear()
        
        # 加载每个品种的数据
        for symbol in symbols:
            try:
                if self.data_manager:
                    # 从数据管理器获取历史数据
                    df = self.data_manager.get_history_data(
                        symbol, self.frequency, 
                        start_date=self.start_date,
                        end_date=self.end_date
                    )
                else:
                    # 生成模拟数据
                    df = self._generate_mock_data(symbol)
                    
                if df is not None and not df.empty:
                    # 确保数据按时间排序
                    df = df.sort_index()
                    self.historical_data[symbol] = df
                    self.data_cursor[symbol] = 0
                    logger.info(f"{symbol} 数据加载完成: {len(df)} 条记录")
                else:
                    logger.warning(f"{symbol} 数据为空")
                    
            except Exception as e:
                logger.error(f"加载 {symbol} 数据失败: {e}")
                
        logger.info(f"历史数据加载完成，共 {len(self.historical_data)} 个品种")
        
    def _generate_mock_data(self, symbol: str) -> pd.DataFrame:
        """生成模拟数据"""
        # 生成日期范围
        date_range = pd.date_range(
            start=self.start_date,
            end=self.end_date,
            freq='D'
        )
        
        # 生成随机价格数据
        np.random.seed(42)  # 固定随机种子
        n_days = len(date_range)
        
        # 初始价格
        initial_price = 10.0
        
        # 生成价格序列（随机游走）
        returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
        prices = [initial_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.1))  # 价格不能为负
            
        # 创建OHLCV数据
        data = []
        for i, (dt, close) in enumerate(zip(date_range, prices)):
            # 生成开高低价
            volatility = 0.01
            high = close * (1 + np.random.uniform(0, volatility))
            low = close * (1 - np.random.uniform(0, volatility))
            
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] * (1 + np.random.uniform(-0.005, 0.005))
                
            volume = np.random.randint(1000, 10000)
            turnover = (high + low + open_price + close) / 4 * volume
            
            data.append({
                'datetime': dt,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume,
                'turnover': round(turnover, 2)
            })
            
        df = pd.DataFrame(data)
        df.set_index('datetime', inplace=True)
        
        return df
        
    def add_strategy(self, strategy_name: str, strategy_instance, 
                    callback: Callable = None):
        """添加策略"""
        self.strategies[strategy_name] = strategy_instance
        
        if callback:
            self.strategy_callbacks[strategy_name].append(callback)
            
        logger.info(f"添加策略: {strategy_name}")
        
    def run_backtest(self, progress_callback: Callable = None) -> Dict:
        """运行回测"""
        if not self.historical_data:
            raise ValueError("请先加载历史数据")
            
        if not self.strategies:
            raise ValueError("请先添加策略")
            
        logger.info("开始回测...")
        self.is_running = True
        self.current_progress = 0.0
        
        try:
            # 初始化回测环境
            self._init_backtest_environment()
            
            # 获取所有时间点
            all_datetimes = self._get_all_datetimes()
            total_points = len(all_datetimes)
            
            logger.info(f"回测时间点数量: {total_points}")
            
            # 逐个时间点回测
            for i, dt in enumerate(all_datetimes):
                if not self.is_running:
                    break
                    
                # 更新当前时间
                self.current_datetime = dt
                
                # 更新市场数据
                self._update_market_data(dt)
                
                # 执行策略
                self._execute_strategies(dt)
                
                # 处理订单
                self._process_orders(dt)
                
                # 更新统计
                self._update_statistics(dt)
                
                # 更新进度
                self.current_progress = (i + 1) / total_points
                if progress_callback:
                    progress_callback(self.current_progress)
                    
            # 生成回测报告
            results = self._generate_results()
            
            logger.info("回测完成")
            return results
            
        except Exception as e:
            logger.error(f"回测异常: {e}")
            raise
        finally:
            self.is_running = False
            
    def _init_backtest_environment(self):
        """初始化回测环境"""
        # 重置交易模块
        if self.trader:
            self.trader._orders.clear()
            self.trader._trades.clear()
            self.trader._positions.clear()
            
            # 重置账户
            account = self.trader.get_account()
            if account:
                account.balance = self.initial_capital
                account.available = self.initial_capital
                account.frozen = 0.0
                
        # 重置投资组合
        if self.portfolio:
            self.portfolio._positions.clear()
            self.portfolio._trades.clear()
            self.portfolio._daily_pnl.clear()
            self.portfolio._daily_balance.clear()
            self.portfolio.current_capital = self.initial_capital
            
        # 重置统计数据
        self.daily_results.clear()
        self.trade_results.clear()
        
        # 重置数据游标
        for symbol in self.data_cursor:
            self.data_cursor[symbol] = 0
            
    def _get_all_datetimes(self) -> List[datetime]:
        """获取所有时间点"""
        all_datetimes = set()
        
        for symbol, df in self.historical_data.items():
            all_datetimes.update(df.index.tolist())
            
        return sorted(list(all_datetimes))
        
    def _update_market_data(self, dt: datetime):
        """更新市场数据"""
        # 为每个品种更新当前数据
        for symbol, df in self.historical_data.items():
            cursor = self.data_cursor[symbol]
            
            # 查找当前时间点的数据
            if cursor < len(df) and df.index[cursor] <= dt:
                row = df.iloc[cursor]
                
                # 创建Bar数据
                bar = BarData(
                    symbol=symbol,
                    exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
                    datetime=dt,
                    interval=self.frequency,
                    open_price=row['open'],
                    high_price=row['high'],
                    low_price=row['low'],
                    close_price=row['close'],
                    volume=row['volume'],
                    turnover=row.get('turnover', 0.0),
                    open_interest=row.get('open_interest', 0.0)
                )
                
                # 发送Bar数据事件
                event = Event(
                    type=EventType.BAR_DATA,
                    data=bar,
                    source="BacktestEngine"
                )
                if self.event_engine:
                    self.event_engine.put(event)
                    
                # 更新游标
                self.data_cursor[symbol] += 1
                
    def _execute_strategies(self, dt: datetime):
        """执行策略"""
        for strategy_name, strategy in self.strategies.items():
            try:
                # 调用策略的on_bar方法
                if hasattr(strategy, 'on_bar'):
                    strategy.on_bar(dt)
                    
                # 调用策略回调
                for callback in self.strategy_callbacks[strategy_name]:
                    callback(strategy, dt)
                    
            except Exception as e:
                logger.error(f"策略 {strategy_name} 执行异常: {e}")
                
    def _process_orders(self, dt: datetime):
        """处理订单"""
        if not self.trader:
            return
            
        # 获取所有待处理订单
        pending_orders = self.trader.get_orders(status=OrderStatus.PENDING)
        
        for order in pending_orders:
            try:
                # 模拟订单成交
                self._simulate_order_fill(order, dt)
                
            except Exception as e:
                logger.error(f"处理订单 {order.orderid} 异常: {e}")
                
    def _simulate_order_fill(self, order: OrderData, dt: datetime):
        """模拟订单成交"""
        # 获取当前市场价格
        symbol = order.symbol
        if symbol not in self.historical_data:
            return
            
        df = self.historical_data[symbol]
        cursor = self.data_cursor[symbol] - 1  # 当前数据位置
        
        if cursor < 0 or cursor >= len(df):
            return
            
        current_bar = df.iloc[cursor]
        
        # 判断是否能成交
        can_fill = False
        fill_price = order.price
        
        if order.type == OrderType.MARKET:
            # 市价单直接成交
            can_fill = True
            fill_price = current_bar['close']
            
        elif order.type == OrderType.LIMIT:
            # 限价单判断成交条件
            if order.direction == Direction.LONG:
                # 买入限价单：当前最低价 <= 委托价
                if current_bar['low'] <= order.price:
                    can_fill = True
                    fill_price = min(order.price, current_bar['open'])
            else:
                # 卖出限价单：当前最高价 >= 委托价
                if current_bar['high'] >= order.price:
                    can_fill = True
                    fill_price = max(order.price, current_bar['open'])
                    
        if can_fill:
            # 应用滑点
            if self.slippage_rate > 0:
                if order.direction == Direction.LONG:
                    fill_price *= (1 + self.slippage_rate)
                else:
                    fill_price *= (1 - self.slippage_rate)
                    
            # 创建成交记录
            trade = TradeData(
                tradeid=f"BT_{len(self.trade_results)+1:06d}",
                orderid=order.orderid,
                symbol=order.symbol,
                exchange=order.exchange,
                direction=order.direction,
                offset=order.offset,
                price=fill_price,
                volume=order.volume,
                datetime=dt
            )
            
            # 更新订单状态
            order.status = OrderStatus.FILLED
            order.traded = order.volume
            
            # 保存成交记录
            if self.trader:
                self.trader._trades[trade.tradeid] = trade
                
            # 发送成交事件
            event = Event(
                type=EventType.ORDER_FILLED,
                data=trade,
                source="BacktestEngine"
            )
            if self.event_engine:
                self.event_engine.put(event)
                
            logger.debug(f"订单成交: {order.orderid} {trade.volume}@{trade.price}")
            
    def _update_statistics(self, dt: datetime):
        """更新统计数据"""
        # 每日统计（简化处理，实际应该按交易日）
        if dt.hour == 15 and dt.minute == 0:  # 收盘时间
            self._record_daily_result(dt.date())
            
    def _record_daily_result(self, trade_date: date):
        """记录每日结果"""
        if not self.portfolio:
            return
            
        # 获取当日统计
        stats = self.portfolio.get_statistics()
        
        daily_result = {
            'date': trade_date,
            'capital': stats['current_capital'],
            'pnl': stats['total_pnl'],
            'commission': stats['total_commission'],
            'trades': stats['total_trades'],
            'positions': stats['position_count']
        }
        
        self.daily_results.append(daily_result)
        
    def _generate_results(self) -> Dict:
        """生成回测结果"""
        if not self.portfolio:
            return {}
            
        # 获取最终统计
        final_stats = self.portfolio.get_statistics()
        
        # 计算性能指标
        performance = self._calculate_performance_metrics()
        
        results = {
            'basic_info': {
                'start_date': self.start_date,
                'end_date': self.end_date,
                'initial_capital': self.initial_capital,
                'final_capital': final_stats['current_capital'],
                'total_return': final_stats['total_return'],
                'total_trades': final_stats['total_trades'],
                'win_rate': final_stats['win_rate']
            },
            'performance': performance,
            'daily_results': self.daily_results,
            'trade_results': self.trade_results,
            'positions': self.portfolio.get_positions() if self.portfolio else [],
            'trades': self.portfolio.get_trades() if self.portfolio else []
        }
        
        return results
        
    def _calculate_performance_metrics(self) -> Dict:
        """计算性能指标"""
        if not self.daily_results:
            return {}
            
        # 提取每日资金序列
        capitals = [result['capital'] for result in self.daily_results]
        
        if len(capitals) < 2:
            return {}
            
        # 计算每日收益率
        returns = []
        for i in range(1, len(capitals)):
            ret = (capitals[i] - capitals[i-1]) / capitals[i-1]
            returns.append(ret)
            
        returns = np.array(returns)
        
        # 计算性能指标
        total_return = (capitals[-1] - capitals[0]) / capitals[0]
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1
        
        volatility = np.std(returns) * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        max_drawdown = 0
        peak = capitals[0]
        
        for capital in capitals:
            if capital > peak:
                peak = capital
            drawdown = (peak - capital) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
                
        performance = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': annual_return / max_drawdown if max_drawdown > 0 else 0
        }
        
        return performance
        
    def get_progress(self) -> float:
        """获取回测进度"""
        return self.current_progress
        
    def pause(self):
        """暂停回测"""
        self.is_paused = True
        logger.info("回测已暂停")
        
    def resume(self):
        """恢复回测"""
        self.is_paused = False
        logger.info("回测已恢复")
        
    def cancel(self):
        """取消回测"""
        self.is_running = False
        logger.info("回测已取消")
        
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return self.is_running
