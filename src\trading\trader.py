#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易器模块

提供交易下单、撤单、查询持仓等核心交易功能。
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Callable
from collections import defaultdict

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, OrderData, TradeData, PositionData, AccountData,
    OrderStatus, OrderType, Direction, Offset
)


class Trader(BaseEngine):
    """交易器类"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化交易器"""
        super().__init__(main_engine, event_engine)
        
        # 订单管理
        self._orders: Dict[str, OrderData] = {}
        self._trades: Dict[str, TradeData] = {}
        self._positions: Dict[str, PositionData] = {}
        self._accounts: Dict[str, AccountData] = {}
        
        # 订单回调
        self._order_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        self._trade_callbacks: List[Callable] = []
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('trading', {})
            
        # 交易模式
        self.trading_mode = self.config.get('trading', {}).get('mode', 'simulation')
        
        # 费用配置
        self.fees_config = self.config.get('trading', {}).get('fees', {})
        
        # 滑点配置
        self.slippage_config = self.config.get('trading', {}).get('slippage', {})
        
        # 模拟交易参数
        self._simulation_mode = True
        self._next_order_id = 1
        self._next_trade_id = 1
        
        # 注册事件处理器
        if event_engine:
            event_engine.register(EventType.ORDER_SUBMIT, self._on_order_submit)
            event_engine.register(EventType.ORDER_CANCEL, self._on_order_cancel)
            
        logger.info(f"交易器初始化完成，模式: {self.trading_mode}")
        
    def start(self):
        """启动交易器"""
        logger.info("启动交易器...")
        
        # 初始化账户
        self._init_accounts()
        
        logger.info("交易器启动完成")
        
    def stop(self):
        """停止交易器"""
        logger.info("停止交易器...")
        
        # 取消所有未完成订单
        self.cancel_all_orders()
        
        logger.info("交易器已停止")
        
    def _init_accounts(self):
        """初始化账户"""
        if self.trading_mode == 'simulation':
            # 模拟账户
            sim_config = self.config.get('accounts', {}).get('simulation', {})
            account = AccountData(
                accountid=sim_config.get('account_id', 'SIM_001'),
                balance=sim_config.get('initial_balance', 1000000.0),
                frozen=0.0
            )
            self._accounts[account.accountid] = account
            logger.info(f"初始化模拟账户: {account.accountid}, 资金: {account.balance}")
            
        elif self.trading_mode == 'live':
            # 实盘账户（需要连接实际的交易接口）
            live_config = self.config.get('accounts', {}).get('live', {})
            # TODO: 实现实盘账户连接
            logger.warning("实盘交易模式暂未实现")
            
    def submit_order(self, symbol: str, exchange: str, direction: Direction,
                    offset: Offset, price: float, volume: float,
                    order_type: OrderType = OrderType.LIMIT,
                    callback: Callable = None) -> Optional[str]:
        """提交订单"""
        try:
            # 生成订单ID
            order_id = self._generate_order_id()
            
            # 创建订单对象
            order = OrderData(
                orderid=order_id,
                symbol=symbol,
                exchange=exchange,
                type=order_type,
                direction=direction,
                offset=offset,
                price=price,
                volume=volume,
                status=OrderStatus.PENDING
            )
            
            # 风险检查
            if not self._check_order_risk(order):
                logger.warning(f"订单风险检查失败: {order_id}")
                return None
                
            # 资金检查
            if not self._check_order_funds(order):
                logger.warning(f"订单资金检查失败: {order_id}")
                return None
                
            # 保存订单
            self._orders[order_id] = order
            
            # 添加回调
            if callback:
                self._order_callbacks[order_id].append(callback)
                
            # 发送订单提交事件
            event = Event(
                type=EventType.ORDER_SUBMIT,
                data=order,
                source="Trader"
            )
            if self.event_engine:
                self.event_engine.put(event)
                
            logger.info(f"提交订单: {order_id} {symbol} {direction.value} {volume}@{price}")
            return order_id
            
        except Exception as e:
            logger.error(f"提交订单异常: {e}")
            return None
            
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        try:
            if order_id not in self._orders:
                logger.warning(f"订单不存在: {order_id}")
                return False
                
            order = self._orders[order_id]
            
            # 检查订单状态
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                logger.warning(f"订单状态不允许撤销: {order_id} {order.status.value}")
                return False
                
            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            
            # 发送订单撤销事件
            event = Event(
                type=EventType.ORDER_CANCEL,
                data=order,
                source="Trader"
            )
            if self.event_engine:
                self.event_engine.put(event)
                
            logger.info(f"撤销订单: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"撤销订单异常: {e}")
            return False
            
    def cancel_all_orders(self) -> int:
        """撤销所有订单"""
        cancelled_count = 0
        
        for order_id, order in self._orders.items():
            if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]:
                if self.cancel_order(order_id):
                    cancelled_count += 1
                    
        logger.info(f"撤销所有订单完成，共撤销 {cancelled_count} 个订单")
        return cancelled_count
        
    def get_order(self, order_id: str) -> Optional[OrderData]:
        """获取订单"""
        return self._orders.get(order_id)
        
    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[OrderData]:
        """获取订单列表"""
        orders = list(self._orders.values())
        
        # 按品种过滤
        if symbol:
            orders = [order for order in orders if order.symbol == symbol]
            
        # 按状态过滤
        if status:
            orders = [order for order in orders if order.status == status]
            
        return orders
        
    def get_trades(self, symbol: str = None) -> List[TradeData]:
        """获取成交列表"""
        trades = list(self._trades.values())
        
        # 按品种过滤
        if symbol:
            trades = [trade for trade in trades if trade.symbol == symbol]
            
        return trades
        
    def get_position(self, symbol: str, exchange: str, direction: Direction) -> Optional[PositionData]:
        """获取持仓"""
        position_key = f"{symbol}.{exchange}.{direction.value}"
        return self._positions.get(position_key)
        
    def get_positions(self, symbol: str = None) -> List[PositionData]:
        """获取持仓列表"""
        positions = list(self._positions.values())
        
        # 按品种过滤
        if symbol:
            positions = [pos for pos in positions if pos.symbol == symbol]
            
        return positions
        
    def get_account(self, account_id: str = None) -> Optional[AccountData]:
        """获取账户"""
        if account_id is None:
            # 返回第一个账户
            if self._accounts:
                return list(self._accounts.values())[0]
            return None
        return self._accounts.get(account_id)
        
    def get_accounts(self) -> List[AccountData]:
        """获取账户列表"""
        return list(self._accounts.values())
        
    def _generate_order_id(self) -> str:
        """生成订单ID"""
        if self._simulation_mode:
            order_id = f"SIM_{self._next_order_id:06d}"
            self._next_order_id += 1
            return order_id
        else:
            return str(uuid.uuid4())
            
    def _generate_trade_id(self) -> str:
        """生成成交ID"""
        if self._simulation_mode:
            trade_id = f"TRADE_{self._next_trade_id:06d}"
            self._next_trade_id += 1
            return trade_id
        else:
            return str(uuid.uuid4())
            
    def _check_order_risk(self, order: OrderData) -> bool:
        """检查订单风险"""
        # TODO: 实现风险检查逻辑
        # 1. 检查单笔订单限额
        # 2. 检查持仓限额
        # 3. 检查交易时间
        return True
        
    def _check_order_funds(self, order: OrderData) -> bool:
        """检查订单资金"""
        try:
            # 获取账户
            account = self.get_account()
            if not account:
                return False
                
            # 计算所需资金
            required_funds = self._calculate_required_funds(order)
            
            # 检查可用资金
            if account.available < required_funds:
                logger.warning(f"资金不足: 需要 {required_funds}, 可用 {account.available}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"资金检查异常: {e}")
            return False
            
    def _calculate_required_funds(self, order: OrderData) -> float:
        """计算所需资金"""
        # 简化计算，实际应根据品种类型、保证金比例等计算
        if order.direction == Direction.LONG:
            return order.price * order.volume
        else:
            # 做空可能需要保证金
            return order.price * order.volume * 0.1  # 假设10%保证金
            
    def _on_order_submit(self, event: Event):
        """处理订单提交事件"""
        order: OrderData = event.data
        if not order:
            return
            
        # 模拟订单处理
        if self._simulation_mode:
            self._simulate_order_execution(order)
            
    def _on_order_cancel(self, event: Event):
        """处理订单撤销事件"""
        order: OrderData = event.data
        if not order:
            return
            
        # 调用订单回调
        if order.orderid in self._order_callbacks:
            for callback in self._order_callbacks[order.orderid]:
                try:
                    callback(order)
                except Exception as e:
                    logger.error(f"订单回调异常: {e}")
                    
    def _simulate_order_execution(self, order: OrderData):
        """模拟订单执行"""
        try:
            # 更新订单状态为已提交
            order.status = OrderStatus.SUBMITTED
            
            # 模拟成交（简化处理，实际应根据市场价格判断）
            import random
            import time
            
            # 延迟一点时间模拟网络延迟
            time.sleep(0.1)
            
            # 随机决定是否成交（90%概率成交）
            if random.random() < 0.9:
                # 生成成交
                trade_id = self._generate_trade_id()
                trade = TradeData(
                    tradeid=trade_id,
                    orderid=order.orderid,
                    symbol=order.symbol,
                    exchange=order.exchange,
                    direction=order.direction,
                    offset=order.offset,
                    price=order.price,
                    volume=order.volume
                )
                
                # 保存成交
                self._trades[trade_id] = trade
                
                # 更新订单
                order.traded = order.volume
                order.status = OrderStatus.FILLED
                
                # 更新持仓
                self._update_position(trade)
                
                # 更新账户
                self._update_account(trade)
                
                # 发送成交事件
                event = Event(
                    type=EventType.ORDER_FILLED,
                    data=trade,
                    source="Trader"
                )
                if self.event_engine:
                    self.event_engine.put(event)
                    
                logger.info(f"订单成交: {order.orderid} {trade.volume}@{trade.price}")
                
            else:
                # 订单被拒绝
                order.status = OrderStatus.REJECTED
                logger.warning(f"订单被拒绝: {order.orderid}")
                
        except Exception as e:
            logger.error(f"模拟订单执行异常: {e}")
            order.status = OrderStatus.ERROR
            
    def _update_position(self, trade: TradeData):
        """更新持仓"""
        position_key = f"{trade.symbol}.{trade.exchange}.{trade.direction.value}"
        
        if position_key in self._positions:
            position = self._positions[position_key]
            
            if trade.offset == Offset.OPEN:
                # 开仓
                total_volume = position.volume + trade.volume
                total_cost = position.volume * position.price + trade.volume * trade.price
                position.price = total_cost / total_volume if total_volume > 0 else 0
                position.volume = total_volume
            else:
                # 平仓
                position.volume -= trade.volume
                if position.volume <= 0:
                    del self._positions[position_key]
                    
        else:
            # 新建持仓
            if trade.offset == Offset.OPEN:
                position = PositionData(
                    symbol=trade.symbol,
                    exchange=trade.exchange,
                    direction=trade.direction,
                    volume=trade.volume,
                    price=trade.price
                )
                self._positions[position_key] = position
                
    def _update_account(self, trade: TradeData):
        """更新账户"""
        account = self.get_account()
        if not account:
            return
            
        # 计算交易成本
        cost = trade.price * trade.volume
        
        if trade.direction == Direction.LONG:
            if trade.offset == Offset.OPEN:
                # 买入开仓
                account.balance -= cost
            else:
                # 买入平仓（平空头）
                account.balance -= cost
        else:
            if trade.offset == Offset.OPEN:
                # 卖出开仓
                account.balance += cost
            else:
                # 卖出平仓（平多头）
                account.balance += cost
                
        # 更新可用资金
        account.available = account.balance - account.frozen
        
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return True
