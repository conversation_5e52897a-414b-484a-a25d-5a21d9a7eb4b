#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略管理器

提供策略的加载、管理、监控等功能。
"""

import os
import sys
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Optional, Type, Any
from collections import defaultdict

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, TickData, BarData, OrderData, TradeData
)
from .base import BaseStrategy


class StrategyManager(BaseEngine):
    """策略管理器"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化策略管理器"""
        super().__init__(main_engine, event_engine)
        
        # 策略实例
        self.strategies: Dict[str, BaseStrategy] = {}
        
        # 策略类注册表
        self.strategy_classes: Dict[str, Type[BaseStrategy]] = {}
        
        # 策略配置
        self.strategy_configs: Dict[str, Dict] = {}
        
        # 数据订阅
        self.symbol_strategies: Dict[str, List[str]] = defaultdict(list)
        
        # 引擎引用
        self.trader = None
        self.portfolio = None
        self.data_manager = None
        
        # 策略目录
        self.strategy_dir = Path("strategies")
        self.strategy_dir.mkdir(exist_ok=True)
        
        # 注册事件处理器
        if event_engine:
            event_engine.register(EventType.TICK_DATA, self._on_tick_data)
            event_engine.register(EventType.BAR_DATA, self._on_bar_data)
            event_engine.register(EventType.ORDER_UPDATE, self._on_order_update)
            event_engine.register(EventType.ORDER_FILLED, self._on_trade_update)
            
        logger.info("策略管理器初始化完成")
        
    def start(self):
        """启动策略管理器"""
        logger.info("启动策略管理器...")
        
        # 获取引擎引用
        if self.main_engine:
            self.trader = getattr(self.main_engine, 'trader', None)
            self.portfolio = getattr(self.main_engine, 'portfolio', None)
            self.data_manager = getattr(self.main_engine, 'data_manager', None)
            
        # 自动加载策略类
        self.load_strategy_classes()
        
        logger.info("策略管理器启动完成")
        
    def stop(self):
        """停止策略管理器"""
        logger.info("停止策略管理器...")
        
        # 停止所有策略
        self.stop_all_strategies()
        
        logger.info("策略管理器已停止")
        
    def load_strategy_classes(self):
        """加载策略类"""
        logger.info("加载策略类...")
        
        # 添加策略目录到Python路径
        if str(self.strategy_dir) not in sys.path:
            sys.path.insert(0, str(self.strategy_dir))
            
        # 扫描策略文件
        for py_file in self.strategy_dir.glob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                # 导入模块
                module_name = py_file.stem
                spec = importlib.util.spec_from_file_location(module_name, py_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找策略类
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, BaseStrategy) and 
                        obj != BaseStrategy):
                        
                        self.strategy_classes[name] = obj
                        logger.info(f"加载策略类: {name}")
                        
            except Exception as e:
                logger.error(f"加载策略文件 {py_file} 失败: {e}")
                
        logger.info(f"策略类加载完成，共 {len(self.strategy_classes)} 个")
        
    def register_strategy_class(self, strategy_class: Type[BaseStrategy]):
        """注册策略类"""
        class_name = strategy_class.__name__
        self.strategy_classes[class_name] = strategy_class
        logger.info(f"注册策略类: {class_name}")
        
    def create_strategy(self, strategy_class_name: str, strategy_name: str, 
                       symbols: List[str], parameters: Dict = None) -> bool:
        """创建策略实例"""
        try:
            # 检查策略类是否存在
            if strategy_class_name not in self.strategy_classes:
                logger.error(f"策略类 {strategy_class_name} 不存在")
                return False
                
            # 检查策略名称是否重复
            if strategy_name in self.strategies:
                logger.error(f"策略名称 {strategy_name} 已存在")
                return False
                
            # 创建策略实例
            strategy_class = self.strategy_classes[strategy_class_name]
            strategy = strategy_class(strategy_name, symbols)
            
            # 设置引擎引用
            strategy.set_engines(self.main_engine, self.trader, self.portfolio)
            
            # 设置参数
            if parameters:
                for param_name, param_value in parameters.items():
                    strategy.set_parameter(param_name, param_value)
                    
            # 保存策略
            self.strategies[strategy_name] = strategy
            self.strategy_configs[strategy_name] = {
                'class_name': strategy_class_name,
                'symbols': symbols,
                'parameters': parameters or {}
            }
            
            # 更新品种订阅
            for symbol in symbols:
                self.symbol_strategies[symbol].append(strategy_name)
                
            logger.info(f"创建策略成功: {strategy_name} ({strategy_class_name})")
            return True
            
        except Exception as e:
            logger.error(f"创建策略失败: {e}")
            return False
            
    def start_strategy(self, strategy_name: str) -> bool:
        """启动策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略 {strategy_name} 不存在")
            return False
            
        try:
            strategy = self.strategies[strategy_name]
            strategy.start()
            
            # 订阅数据
            self._subscribe_data(strategy)
            
            logger.info(f"启动策略成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"启动策略失败: {e}")
            return False
            
    def stop_strategy(self, strategy_name: str) -> bool:
        """停止策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略 {strategy_name} 不存在")
            return False
            
        try:
            strategy = self.strategies[strategy_name]
            strategy.stop()
            
            # 取消数据订阅
            self._unsubscribe_data(strategy)
            
            logger.info(f"停止策略成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"停止策略失败: {e}")
            return False
            
    def remove_strategy(self, strategy_name: str) -> bool:
        """移除策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略 {strategy_name} 不存在")
            return False
            
        try:
            # 先停止策略
            self.stop_strategy(strategy_name)
            
            # 移除策略
            strategy = self.strategies.pop(strategy_name)
            config = self.strategy_configs.pop(strategy_name, {})
            
            # 更新品种订阅
            for symbol in config.get('symbols', []):
                if strategy_name in self.symbol_strategies[symbol]:
                    self.symbol_strategies[symbol].remove(strategy_name)
                    
            logger.info(f"移除策略成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"移除策略失败: {e}")
            return False
            
    def start_all_strategies(self) -> int:
        """启动所有策略"""
        started_count = 0
        
        for strategy_name in self.strategies:
            if self.start_strategy(strategy_name):
                started_count += 1
                
        logger.info(f"启动所有策略完成，成功启动 {started_count} 个")
        return started_count
        
    def stop_all_strategies(self) -> int:
        """停止所有策略"""
        stopped_count = 0
        
        for strategy_name in self.strategies:
            if self.stop_strategy(strategy_name):
                stopped_count += 1
                
        logger.info(f"停止所有策略完成，成功停止 {stopped_count} 个")
        return stopped_count
        
    def pause_strategy_trading(self, strategy_name: str) -> bool:
        """暂停策略交易"""
        if strategy_name not in self.strategies:
            return False
            
        strategy = self.strategies[strategy_name]
        strategy.pause_trading()
        return True
        
    def resume_strategy_trading(self, strategy_name: str) -> bool:
        """恢复策略交易"""
        if strategy_name not in self.strategies:
            return False
            
        strategy = self.strategies[strategy_name]
        strategy.resume_trading()
        return True
        
    def get_strategy(self, strategy_name: str) -> Optional[BaseStrategy]:
        """获取策略实例"""
        return self.strategies.get(strategy_name)
        
    def get_strategies(self) -> Dict[str, BaseStrategy]:
        """获取所有策略"""
        return self.strategies.copy()
        
    def get_strategy_classes(self) -> Dict[str, Type[BaseStrategy]]:
        """获取策略类"""
        return self.strategy_classes.copy()
        
    def get_strategy_info(self, strategy_name: str) -> Optional[Dict]:
        """获取策略信息"""
        if strategy_name not in self.strategies:
            return None
            
        strategy = self.strategies[strategy_name]
        config = self.strategy_configs.get(strategy_name, {})
        
        info = {
            'name': strategy_name,
            'class_name': config.get('class_name', ''),
            'symbols': config.get('symbols', []),
            'active': strategy.active,
            'trading': strategy.trading,
            'statistics': strategy.get_statistics(),
            'parameters': strategy.get_parameters_info()
        }
        
        return info
        
    def get_all_strategies_info(self) -> List[Dict]:
        """获取所有策略信息"""
        info_list = []
        
        for strategy_name in self.strategies:
            info = self.get_strategy_info(strategy_name)
            if info:
                info_list.append(info)
                
        return info_list
        
    def update_strategy_parameters(self, strategy_name: str, parameters: Dict) -> bool:
        """更新策略参数"""
        if strategy_name not in self.strategies:
            return False
            
        try:
            strategy = self.strategies[strategy_name]
            
            for param_name, param_value in parameters.items():
                if not strategy.set_parameter(param_name, param_value):
                    logger.warning(f"设置参数失败: {param_name} = {param_value}")
                    
            # 更新配置
            if strategy_name in self.strategy_configs:
                self.strategy_configs[strategy_name]['parameters'].update(parameters)
                
            logger.info(f"更新策略参数成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"更新策略参数失败: {e}")
            return False
            
    def _subscribe_data(self, strategy: BaseStrategy):
        """订阅数据"""
        if not self.data_manager:
            return
            
        for symbol in strategy.symbols:
            try:
                # 订阅Tick数据
                self.data_manager.subscribe_tick(symbol, self._create_tick_callback(strategy))
                
                # 订阅K线数据
                self.data_manager.subscribe_bar(symbol, "1m", self._create_bar_callback(strategy))
                
            except Exception as e:
                logger.error(f"订阅数据失败 {symbol}: {e}")
                
    def _unsubscribe_data(self, strategy: BaseStrategy):
        """取消订阅数据"""
        if not self.data_manager:
            return
            
        for symbol in strategy.symbols:
            try:
                # 这里简化处理，实际应该精确取消订阅
                pass
            except Exception as e:
                logger.error(f"取消订阅数据失败 {symbol}: {e}")
                
    def _create_tick_callback(self, strategy: BaseStrategy):
        """创建Tick数据回调"""
        def callback(tick: TickData):
            strategy.on_tick(tick)
        return callback
        
    def _create_bar_callback(self, strategy: BaseStrategy):
        """创建K线数据回调"""
        def callback(bar: BarData):
            strategy.on_bar(bar)
        return callback
        
    def _on_tick_data(self, event: Event):
        """处理Tick数据事件"""
        tick: TickData = event.data
        if not tick:
            return
            
        # 分发给相关策略
        for strategy_name in self.symbol_strategies.get(tick.symbol, []):
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                strategy.on_tick(tick)
                
    def _on_bar_data(self, event: Event):
        """处理K线数据事件"""
        bar: BarData = event.data
        if not bar:
            return
            
        # 分发给相关策略
        for strategy_name in self.symbol_strategies.get(bar.symbol, []):
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                strategy.on_bar(bar)
                
    def _on_order_update(self, event: Event):
        """处理订单更新事件"""
        order: OrderData = event.data
        if not order:
            return
            
        # 分发给所有策略
        for strategy in self.strategies.values():
            strategy.on_order(order)
            
    def _on_trade_update(self, event: Event):
        """处理成交更新事件"""
        trade: TradeData = event.data
        if not trade:
            return
            
        # 分发给相关策略
        for strategy_name in self.symbol_strategies.get(trade.symbol, []):
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                strategy.on_trade(trade)
                
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return True
