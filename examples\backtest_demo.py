#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测模块演示脚本

演示如何使用回测引擎进行策略回测和结果分析。
"""

import sys
import os
from pathlib import Path
from datetime import datetime, date, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.core.base import Direction, Offset, OrderType
from src.backtest.engine import BacktestEngine
from src.backtest.analyzer import BacktestAnalyzer
from src.backtest.reporter import BacktestReporter
from loguru import logger


class SimpleMovingAverageStrategy:
    """简单移动平均策略"""
    
    def __init__(self, name: str, short_period: int = 5, long_period: int = 20):
        self.name = name
        self.short_period = short_period
        self.long_period = long_period
        
        # 价格历史
        self.price_history = []
        self.position = 0  # 0: 空仓, 1: 多头, -1: 空头
        
        # 统计
        self.signals_count = 0
        self.trades_count = 0
        
    def on_bar(self, dt: datetime):
        """处理K线数据"""
        # 这里简化处理，实际应该从市场数据获取价格
        # 为了演示，我们使用模拟价格
        import random
        price = 10.0 + random.uniform(-1, 1)  # 模拟价格
        self.price_history.append(price)
        
        # 保持历史数据长度
        if len(self.price_history) > self.long_period:
            self.price_history.pop(0)
            
        # 计算移动平均
        if len(self.price_history) >= self.long_period:
            short_ma = sum(self.price_history[-self.short_period:]) / self.short_period
            long_ma = sum(self.price_history[-self.long_period:]) / self.long_period
            
            # 生成交易信号
            if short_ma > long_ma and self.position <= 0:
                # 金叉，买入信号
                self.position = 1
                self.signals_count += 1
                self.trades_count += 1
                logger.debug(f"{dt}: 买入信号 - 短期MA: {short_ma:.2f}, 长期MA: {long_ma:.2f}")
                
            elif short_ma < long_ma and self.position >= 0:
                # 死叉，卖出信号
                self.position = -1
                self.signals_count += 1
                self.trades_count += 1
                logger.debug(f"{dt}: 卖出信号 - 短期MA: {short_ma:.2f}, 长期MA: {long_ma:.2f}")


class BuyAndHoldStrategy:
    """买入持有策略"""
    
    def __init__(self, name: str):
        self.name = name
        self.position = 0
        self.first_bar = True
        
    def on_bar(self, dt: datetime):
        """处理K线数据"""
        if self.first_bar:
            # 第一根K线买入
            self.position = 1
            self.first_bar = False
            logger.debug(f"{dt}: 买入持有策略 - 初始买入")


def progress_callback(progress: float):
    """回测进度回调"""
    if int(progress * 100) % 10 == 0:  # 每10%显示一次
        logger.info(f"回测进度: {progress:.1%}")


def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("回测模块演示开始")
    logger.info("=" * 50)
    
    # 创建主引擎
    main_engine = MainEngine()
    
    try:
        # 启动主引擎
        main_engine.start()
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(main_engine, main_engine.event_engine)
        
        # 启动回测引擎
        backtest_engine.start()
        
        # 设置回测参数
        start_date = date(2023, 1, 1)
        end_date = date(2023, 3, 31)  # 3个月回测
        initial_capital = 1000000.0
        
        logger.info(f"回测参数设置:")
        logger.info(f"  开始日期: {start_date}")
        logger.info(f"  结束日期: {end_date}")
        logger.info(f"  初始资金: {initial_capital:,.0f}")
        
        backtest_engine.set_parameters(
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            commission_rate=0.0003,  # 万分之3手续费
            slippage_rate=0.0001     # 万分之1滑点
        )
        
        # 加载历史数据
        symbols = ["000001.SZ", "600000.SH", "000300.SH"]
        logger.info(f"加载历史数据: {symbols}")
        backtest_engine.load_data(symbols)
        
        # 显示数据加载情况
        for symbol, df in backtest_engine.historical_data.items():
            logger.info(f"  {symbol}: {len(df)} 条记录")
            
        # 创建策略
        strategies = [
            SimpleMovingAverageStrategy("MA_5_20", 5, 20),
            SimpleMovingAverageStrategy("MA_10_30", 10, 30),
            BuyAndHoldStrategy("BuyHold")
        ]
        
        # 运行多个策略的回测
        all_results = []
        strategy_names = []
        
        for strategy in strategies:
            logger.info(f"\n开始回测策略: {strategy.name}")
            
            # 重置回测环境
            backtest_engine._init_backtest_environment()
            
            # 添加策略
            backtest_engine.strategies.clear()
            backtest_engine.add_strategy(strategy.name, strategy)
            
            # 运行回测
            results = backtest_engine.run_backtest(progress_callback)
            
            # 保存结果
            all_results.append(results)
            strategy_names.append(strategy.name)
            
            # 显示基本结果
            basic_info = results.get('basic_info', {})
            logger.info(f"策略 {strategy.name} 回测完成:")
            logger.info(f"  最终资金: {basic_info.get('final_capital', 0):,.2f}")
            logger.info(f"  总收益率: {basic_info.get('total_return', 0):.2%}")
            logger.info(f"  总交易次数: {basic_info.get('total_trades', 0)}")
            logger.info(f"  胜率: {basic_info.get('win_rate', 0):.2%}")
            
        # 创建分析器和报告生成器
        analyzer = BacktestAnalyzer()
        reporter = BacktestReporter()
        
        # 分析每个策略的结果
        logger.info("\n" + "=" * 30)
        logger.info("详细分析结果")
        logger.info("=" * 30)
        
        detailed_results = []
        
        for i, (results, strategy_name) in enumerate(zip(all_results, strategy_names)):
            logger.info(f"\n分析策略: {strategy_name}")
            
            # 加载结果到分析器
            analyzer.load_results(results)
            
            # 计算详细指标
            detailed_metrics = analyzer.calculate_detailed_metrics()
            detailed_results.append(detailed_metrics)
            
            # 显示关键指标
            return_metrics = detailed_metrics.get('return_metrics', {})
            risk_metrics = detailed_metrics.get('risk_metrics', {})
            risk_adj_metrics = detailed_metrics.get('risk_adjusted_metrics', {})
            
            logger.info(f"  年化收益率: {return_metrics.get('annual_return', 0):.2%}")
            logger.info(f"  年化波动率: {risk_metrics.get('volatility', 0):.2%}")
            logger.info(f"  最大回撤: {risk_metrics.get('max_drawdown', 0):.2%}")
            logger.info(f"  夏普比率: {risk_adj_metrics.get('sharpe_ratio', 0):.4f}")
            logger.info(f"  索提诺比率: {risk_adj_metrics.get('sortino_ratio', 0):.4f}")
            
            # 生成个别策略报告
            try:
                report_dir = Path("data/backtest_results")
                report_dir.mkdir(parents=True, exist_ok=True)
                
                # 生成文本报告
                report_path = report_dir / f"{strategy_name}_report.txt"
                report_content = analyzer.generate_report(str(report_path))
                logger.info(f"  报告已生成: {report_path}")
                
                # 尝试生成HTML报告
                try:
                    html_path = report_dir / f"{strategy_name}_report.html"
                    reporter.generate_html_report(detailed_metrics, str(html_path))
                    logger.info(f"  HTML报告已生成: {html_path}")
                except Exception as e:
                    logger.warning(f"  HTML报告生成失败: {e}")
                    
                # 尝试导出Excel
                try:
                    excel_path = report_dir / f"{strategy_name}_data.xlsx"
                    analyzer.export_results(str(excel_path), 'excel')
                    logger.info(f"  数据已导出: {excel_path}")
                except Exception as e:
                    logger.warning(f"  Excel导出失败: {e}")
                    
            except Exception as e:
                logger.error(f"  报告生成失败: {e}")
                
        # 生成策略对比报告
        logger.info("\n" + "=" * 30)
        logger.info("策略对比分析")
        logger.info("=" * 30)
        
        try:
            report_dir = Path("data/backtest_results")
            comparison_path = report_dir / "strategy_comparison.txt"
            
            reporter.generate_comparison_report(
                detailed_results, strategy_names, str(comparison_path)
            )
            
            logger.info(f"策略对比报告已生成: {comparison_path}")
            
            # 显示对比摘要
            with open(comparison_path, 'r', encoding='utf-8') as f:
                comparison_content = f.read()
                
            # 提取推荐策略部分
            lines = comparison_content.split('\n')
            in_recommendation = False
            for line in lines:
                if '推荐策略:' in line:
                    in_recommendation = True
                if in_recommendation:
                    logger.info(line)
                    
        except Exception as e:
            logger.error(f"策略对比报告生成失败: {e}")
            
        # 生成摘要报告
        logger.info("\n" + "=" * 30)
        logger.info("最佳策略推荐")
        logger.info("=" * 30)
        
        # 找出最佳策略
        best_return_idx = 0
        best_sharpe_idx = 0
        min_drawdown_idx = 0
        
        best_return = -999
        best_sharpe = -999
        min_drawdown = 999
        
        for i, metrics in enumerate(detailed_results):
            return_metrics = metrics.get('return_metrics', {})
            risk_metrics = metrics.get('risk_metrics', {})
            risk_adj_metrics = metrics.get('risk_adjusted_metrics', {})
            
            annual_return = return_metrics.get('annual_return', 0)
            sharpe_ratio = risk_adj_metrics.get('sharpe_ratio', 0)
            max_drawdown = risk_metrics.get('max_drawdown', 1)
            
            if annual_return > best_return:
                best_return = annual_return
                best_return_idx = i
                
            if sharpe_ratio > best_sharpe:
                best_sharpe = sharpe_ratio
                best_sharpe_idx = i
                
            if max_drawdown < min_drawdown:
                min_drawdown = max_drawdown
                min_drawdown_idx = i
                
        logger.info(f"最高收益策略: {strategy_names[best_return_idx]} ({best_return:.2%})")
        logger.info(f"最佳风险调整收益策略: {strategy_names[best_sharpe_idx]} (夏普比率: {best_sharpe:.4f})")
        logger.info(f"最小回撤策略: {strategy_names[min_drawdown_idx]} ({min_drawdown:.2%})")
        
        # 综合评分推荐
        best_overall_idx = best_sharpe_idx  # 以夏普比率为主要标准
        best_strategy = strategy_names[best_overall_idx]
        best_metrics = detailed_results[best_overall_idx]
        
        logger.info(f"\n综合推荐策略: {best_strategy}")
        
        # 显示推荐策略的详细信息
        summary_report = reporter.generate_summary_report(best_metrics)
        logger.info("\n推荐策略详细信息:")
        for line in summary_report.split('\n'):
            if line.strip():
                logger.info(line)
                
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
        
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理资源
        logger.info("清理资源...")
        if 'backtest_engine' in locals():
            backtest_engine.stop()
        main_engine.stop()
        
    logger.info("=" * 50)
    logger.info("回测模块演示结束")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
