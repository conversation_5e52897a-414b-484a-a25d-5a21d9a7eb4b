# 系统配置文件
system:
  name: "WtPy量化交易系统"
  version: "1.0.0"
  debug: true
  timezone: "Asia/Shanghai"
  
# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  rotation: "1 day"
  retention: "30 days"
  compression: "zip"
  file_path: "data/logs/system.log"
  
# 数据库配置
database:
  # SQLite配置（默认）
  sqlite:
    path: "data/database.db"
    echo: false
    pool_size: 10
    max_overflow: 20
    
  # MongoDB配置（可选）
  mongodb:
    host: "localhost"
    port: 27017
    database: "wtpy_quant"
    username: ""
    password: ""
    
# 缓存配置
cache:
  type: "memory"  # memory, redis
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: ""
    
# 事件引擎配置
event_engine:
  thread_count: 4
  queue_size: 1000
  timer_interval: 1.0  # 秒
  
# 性能监控配置
monitor:
  enabled: true
  cpu_threshold: 80.0  # CPU使用率阈值
  memory_threshold: 80.0  # 内存使用率阈值
  disk_threshold: 90.0  # 磁盘使用率阈值
  check_interval: 60  # 检查间隔（秒）
  
# GUI配置
gui:
  theme: "dark"  # light, dark
  language: "zh_CN"  # zh_CN, en_US
  window:
    width: 1400
    height: 900
    resizable: true
    
# API服务配置
api:
  enabled: false
  host: "0.0.0.0"
  port: 8000
  cors_origins: ["*"]
  
# 安全配置
security:
  encryption_key: "your-secret-key-here"
  token_expire_hours: 24
  max_login_attempts: 5
  
# 插件配置
plugins:
  enabled: true
  directory: "plugins"
  auto_load: true
