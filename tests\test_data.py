#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据模块测试

测试数据获取、存储、管理等功能。
"""

import sys
import unittest
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.data.data_manager import DataManager
from src.data.miniqmt_api import MiniqmtApi
from src.data.database import DatabaseManager


class TestDataManager(unittest.TestCase):
    """数据管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.data_manager = DataManager(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'data_manager'):
            self.data_manager.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.data_manager)
        self.assertIsNotNone(self.data_manager.miniqmt_api)
        self.assertIsNotNone(self.data_manager.database)
        
    def test_start_stop(self):
        """测试启动停止"""
        self.data_manager.start()
        self.assertTrue(self.data_manager.database.active)
        
        self.data_manager.stop()
        # 注意：由于xtdata可能未安装，miniqmt_api可能不会激活
        
    def test_subscribe_tick(self):
        """测试订阅Tick数据"""
        # 定义回调函数
        received_data = []
        
        def on_tick(tick_data):
            received_data.append(tick_data)
            
        # 订阅数据（如果xtdata可用）
        success = self.data_manager.subscribe_tick("000001.SZ", on_tick)
        
        # 检查订阅状态
        subscriptions = self.data_manager.get_subscriptions()
        if success:
            self.assertIn("tick_000001.SZ", subscriptions)
        else:
            # 如果xtdata不可用，订阅会失败
            self.assertNotIn("tick_000001.SZ", subscriptions)
            
    def test_subscribe_bar(self):
        """测试订阅K线数据"""
        # 定义回调函数
        received_data = []
        
        def on_bar(bar_data):
            received_data.append(bar_data)
            
        # 订阅数据
        success = self.data_manager.subscribe_bar("000001.SZ", "1m", on_bar)
        
        # 检查订阅状态
        subscriptions = self.data_manager.get_subscriptions()
        if success:
            self.assertIn("bar_000001.SZ_1m", subscriptions)
        else:
            self.assertNotIn("bar_000001.SZ_1m", subscriptions)


class TestMiniqmtApi(unittest.TestCase):
    """MiniQMT接口测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.miniqmt_api = MiniqmtApi(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'miniqmt_api'):
            self.miniqmt_api.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.miniqmt_api)
        self.assertEqual(self.miniqmt_api.host, '127.0.0.1')
        self.assertEqual(self.miniqmt_api.port, 58610)
        
    def test_connect(self):
        """测试连接"""
        # 尝试连接（可能失败，因为MiniQMT可能未运行）
        success = self.miniqmt_api.connect()
        
        # 检查连接状态
        self.assertEqual(self.miniqmt_api.is_connected(), success)
        
    def test_start_stop(self):
        """测试启动停止"""
        success = self.miniqmt_api.start()
        
        if success:
            self.assertTrue(self.miniqmt_api.active)
            
        self.miniqmt_api.stop()
        self.assertFalse(self.miniqmt_api.is_connected())


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.database = DatabaseManager(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'database'):
            self.database.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.database)
        self.assertEqual(self.database.sqlite_path, 'data/database.db')
        
    def test_start_stop(self):
        """测试启动停止"""
        self.database.start()
        self.assertTrue(self.database.active)
        self.assertIsNotNone(self.database.sqlite_engine)
        
        self.database.stop()
        # SQLite引擎在stop后仍然存在，但连接已关闭


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
