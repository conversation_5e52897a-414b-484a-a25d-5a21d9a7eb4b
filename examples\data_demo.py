#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据接口演示脚本

演示如何使用数据管理器获取和订阅行情数据。
"""

import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.data.data_manager import DataManager
from loguru import logger


def on_tick_data(tick_data):
    """Tick数据回调函数"""
    logger.info(f"收到Tick数据: {tick_data.symbol} {tick_data.last_price} {tick_data.datetime}")


def on_bar_data(bar_data):
    """K线数据回调函数"""
    logger.info(f"收到K线数据: {bar_data.symbol} {bar_data.interval} "
               f"O:{bar_data.open_price} H:{bar_data.high_price} "
               f"L:{bar_data.low_price} C:{bar_data.close_price} "
               f"V:{bar_data.volume} {bar_data.datetime}")


def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("数据接口演示开始")
    logger.info("=" * 50)
    
    # 创建主引擎
    main_engine = MainEngine()
    
    try:
        # 启动主引擎
        main_engine.start()
        
        # 创建数据管理器
        data_manager = DataManager(main_engine, main_engine.event_engine)
        
        # 启动数据管理器
        data_manager.start()
        
        # 检查连接状态
        if data_manager.miniqmt_api.is_connected():
            logger.info("MiniQMT连接成功")
            
            # 订阅Tick数据
            symbols = ["000001.SZ", "600000.SH", "000300.SH"]
            for symbol in symbols:
                success = data_manager.subscribe_tick(symbol, on_tick_data)
                if success:
                    logger.info(f"订阅Tick数据成功: {symbol}")
                else:
                    logger.warning(f"订阅Tick数据失败: {symbol}")
                    
            # 订阅K线数据
            intervals = ["1m", "5m"]
            for symbol in symbols[:2]:  # 只订阅前两个品种的K线
                for interval in intervals:
                    success = data_manager.subscribe_bar(symbol, interval, on_bar_data)
                    if success:
                        logger.info(f"订阅K线数据成功: {symbol} {interval}")
                    else:
                        logger.warning(f"订阅K线数据失败: {symbol} {interval}")
                        
            # 获取历史数据
            logger.info("获取历史数据...")
            for symbol in symbols:
                try:
                    # 获取日线数据
                    df = data_manager.get_history_data(symbol, "1d", count=10)
                    if df is not None and not df.empty:
                        logger.info(f"{symbol} 历史日线数据: {len(df)}条")
                        logger.info(f"最新收盘价: {df['close'].iloc[-1] if 'close' in df.columns else 'N/A'}")
                    else:
                        logger.warning(f"{symbol} 历史数据为空")
                        
                    # 获取分钟线数据
                    bars = data_manager.get_bar_data(symbol, "1m", count=5)
                    if bars:
                        logger.info(f"{symbol} 最新分钟线数据: {len(bars)}条")
                        latest_bar = bars[-1]
                        logger.info(f"最新K线: O:{latest_bar.open_price} H:{latest_bar.high_price} "
                                   f"L:{latest_bar.low_price} C:{latest_bar.close_price}")
                    else:
                        logger.warning(f"{symbol} 分钟线数据为空")
                        
                except Exception as e:
                    logger.error(f"获取 {symbol} 历史数据失败: {e}")
                    
            # 显示订阅状态
            subscriptions = data_manager.get_subscriptions()
            logger.info(f"当前订阅数量: {len(subscriptions)}")
            for sub_key, sub_info in subscriptions.items():
                logger.info(f"订阅: {sub_key} -> {sub_info['type']}")
                
            # 运行一段时间接收数据
            logger.info("开始接收实时数据，运行30秒...")
            start_time = time.time()
            while time.time() - start_time < 30:
                time.sleep(1)
                
                # 每10秒显示一次缓存状态
                if int(time.time() - start_time) % 10 == 0:
                    logger.info("系统运行中...")
                    
        else:
            logger.warning("MiniQMT连接失败，可能原因：")
            logger.warning("1. MiniQMT软件未启动")
            logger.warning("2. xtdata模块未安装")
            logger.warning("3. 连接参数配置错误")
            
            # 演示数据库功能
            logger.info("演示数据库功能...")
            
            # 创建模拟数据
            from src.core.base import TickData, BarData
            from datetime import datetime
            
            # 模拟Tick数据
            tick = TickData(
                symbol="000001.SZ",
                exchange="SZ",
                datetime=datetime.now(),
                last_price=10.50,
                volume=1000,
                turnover=10500.0
            )
            
            # 保存到数据库
            success = data_manager.database.save_tick_data(tick)
            if success:
                logger.info("模拟Tick数据保存成功")
            else:
                logger.warning("模拟Tick数据保存失败")
                
            # 模拟K线数据
            bar = BarData(
                symbol="000001.SZ",
                exchange="SZ",
                datetime=datetime.now(),
                interval="1m",
                open_price=10.45,
                high_price=10.55,
                low_price=10.40,
                close_price=10.50,
                volume=1000,
                turnover=10500.0,
                open_interest=0
            )
            
            # 保存到数据库
            success = data_manager.database.save_bar_data(bar)
            if success:
                logger.info("模拟K线数据保存成功")
            else:
                logger.warning("模拟K线数据保存失败")
                
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
        
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        
    finally:
        # 清理资源
        logger.info("清理资源...")
        if 'data_manager' in locals():
            data_manager.stop()
        main_engine.stop()
        
    logger.info("=" * 50)
    logger.info("数据接口演示结束")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
