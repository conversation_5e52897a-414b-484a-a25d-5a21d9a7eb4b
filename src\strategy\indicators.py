#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
技术指标库

提供常用的技术分析指标。
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Optional, Union
from collections import deque

from loguru import logger


class IndicatorBase:
    """指标基类"""
    
    def __init__(self, period: int):
        self.period = period
        self.data = deque(maxlen=period * 2)  # 保留更多历史数据
        self.values = deque(maxlen=1000)  # 指标值历史
        
    def update(self, value: float) -> Optional[float]:
        """更新指标"""
        self.data.append(value)
        
        if len(self.data) >= self.period:
            indicator_value = self.calculate()
            self.values.append(indicator_value)
            return indicator_value
            
        return None
        
    def calculate(self) -> float:
        """计算指标值（子类实现）"""
        raise NotImplementedError
        
    def get_value(self, index: int = -1) -> Optional[float]:
        """获取指标值"""
        if len(self.values) > abs(index):
            return self.values[index]
        return None
        
    def get_values(self, count: int = None) -> List[float]:
        """获取指标值列表"""
        if count is None:
            return list(self.values)
        else:
            return list(self.values)[-count:] if len(self.values) >= count else list(self.values)


class SMA(IndicatorBase):
    """简单移动平均线"""
    
    def calculate(self) -> float:
        return sum(self.data) / len(self.data)


class EMA(IndicatorBase):
    """指数移动平均线"""
    
    def __init__(self, period: int):
        super().__init__(period)
        self.alpha = 2.0 / (period + 1)
        self.ema_value = None
        
    def update(self, value: float) -> Optional[float]:
        """更新EMA"""
        self.data.append(value)
        
        if self.ema_value is None:
            if len(self.data) >= self.period:
                # 初始EMA使用SMA
                self.ema_value = sum(list(self.data)[-self.period:]) / self.period
                self.values.append(self.ema_value)
                return self.ema_value
        else:
            # 计算EMA
            self.ema_value = self.alpha * value + (1 - self.alpha) * self.ema_value
            self.values.append(self.ema_value)
            return self.ema_value
            
        return None


class RSI(IndicatorBase):
    """相对强弱指标"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.gains = deque(maxlen=period)
        self.losses = deque(maxlen=period)
        self.prev_price = None
        
    def update(self, price: float) -> Optional[float]:
        """更新RSI"""
        if self.prev_price is not None:
            change = price - self.prev_price
            
            if change > 0:
                self.gains.append(change)
                self.losses.append(0)
            else:
                self.gains.append(0)
                self.losses.append(abs(change))
                
            if len(self.gains) >= self.period:
                avg_gain = sum(self.gains) / self.period
                avg_loss = sum(self.losses) / self.period
                
                if avg_loss == 0:
                    rsi = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    
                self.values.append(rsi)
                self.prev_price = price
                return rsi
                
        self.prev_price = price
        return None


class MACD:
    """MACD指标"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        self.fast_ema = EMA(fast_period)
        self.slow_ema = EMA(slow_period)
        self.signal_ema = EMA(signal_period)
        
        self.macd_values = deque(maxlen=1000)
        self.signal_values = deque(maxlen=1000)
        self.histogram_values = deque(maxlen=1000)
        
    def update(self, price: float) -> Optional[Tuple[float, float, float]]:
        """更新MACD"""
        fast_ema = self.fast_ema.update(price)
        slow_ema = self.slow_ema.update(price)
        
        if fast_ema is not None and slow_ema is not None:
            macd = fast_ema - slow_ema
            self.macd_values.append(macd)
            
            signal = self.signal_ema.update(macd)
            if signal is not None:
                self.signal_values.append(signal)
                histogram = macd - signal
                self.histogram_values.append(histogram)
                
                return macd, signal, histogram
                
        return None
        
    def get_values(self) -> Tuple[List[float], List[float], List[float]]:
        """获取MACD值"""
        return (list(self.macd_values), 
                list(self.signal_values), 
                list(self.histogram_values))


class BollingerBands:
    """布林带"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        self.period = period
        self.std_dev = std_dev
        self.data = deque(maxlen=period)
        
        self.upper_values = deque(maxlen=1000)
        self.middle_values = deque(maxlen=1000)
        self.lower_values = deque(maxlen=1000)
        
    def update(self, price: float) -> Optional[Tuple[float, float, float]]:
        """更新布林带"""
        self.data.append(price)
        
        if len(self.data) >= self.period:
            prices = list(self.data)
            middle = sum(prices) / len(prices)
            std = np.std(prices)
            
            upper = middle + self.std_dev * std
            lower = middle - self.std_dev * std
            
            self.upper_values.append(upper)
            self.middle_values.append(middle)
            self.lower_values.append(lower)
            
            return upper, middle, lower
            
        return None
        
    def get_values(self) -> Tuple[List[float], List[float], List[float]]:
        """获取布林带值"""
        return (list(self.upper_values),
                list(self.middle_values),
                list(self.lower_values))


class KDJ:
    """KDJ指标"""
    
    def __init__(self, period: int = 9, k_period: int = 3, d_period: int = 3):
        self.period = period
        self.k_period = k_period
        self.d_period = d_period
        
        self.highs = deque(maxlen=period)
        self.lows = deque(maxlen=period)
        self.closes = deque(maxlen=period)
        
        self.k_values = deque(maxlen=1000)
        self.d_values = deque(maxlen=1000)
        self.j_values = deque(maxlen=1000)
        
        self.k_sma = SMA(k_period)
        self.d_sma = SMA(d_period)
        
    def update(self, high: float, low: float, close: float) -> Optional[Tuple[float, float, float]]:
        """更新KDJ"""
        self.highs.append(high)
        self.lows.append(low)
        self.closes.append(close)
        
        if len(self.closes) >= self.period:
            highest = max(self.highs)
            lowest = min(self.lows)
            
            if highest == lowest:
                rsv = 50
            else:
                rsv = (close - lowest) / (highest - lowest) * 100
                
            k = self.k_sma.update(rsv)
            if k is not None:
                d = self.d_sma.update(k)
                if d is not None:
                    j = 3 * k - 2 * d
                    
                    self.k_values.append(k)
                    self.d_values.append(d)
                    self.j_values.append(j)
                    
                    return k, d, j
                    
        return None
        
    def get_values(self) -> Tuple[List[float], List[float], List[float]]:
        """获取KDJ值"""
        return (list(self.k_values),
                list(self.d_values),
                list(self.j_values))


class ATR(IndicatorBase):
    """平均真实波幅"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.prev_close = None
        self.tr_values = deque(maxlen=period)
        
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新ATR"""
        if self.prev_close is not None:
            tr1 = high - low
            tr2 = abs(high - self.prev_close)
            tr3 = abs(low - self.prev_close)
            
            tr = max(tr1, tr2, tr3)
            self.tr_values.append(tr)
            
            if len(self.tr_values) >= self.period:
                atr = sum(self.tr_values) / len(self.tr_values)
                self.values.append(atr)
                self.prev_close = close
                return atr
                
        self.prev_close = close
        return None


class StochasticOscillator:
    """随机振荡器"""
    
    def __init__(self, k_period: int = 14, d_period: int = 3):
        self.k_period = k_period
        self.d_period = d_period
        
        self.highs = deque(maxlen=k_period)
        self.lows = deque(maxlen=k_period)
        self.closes = deque(maxlen=k_period)
        
        self.k_values = deque(maxlen=1000)
        self.d_values = deque(maxlen=1000)
        
        self.d_sma = SMA(d_period)
        
    def update(self, high: float, low: float, close: float) -> Optional[Tuple[float, float]]:
        """更新随机振荡器"""
        self.highs.append(high)
        self.lows.append(low)
        self.closes.append(close)
        
        if len(self.closes) >= self.k_period:
            highest = max(self.highs)
            lowest = min(self.lows)
            
            if highest == lowest:
                k = 50
            else:
                k = (close - lowest) / (highest - lowest) * 100
                
            self.k_values.append(k)
            
            d = self.d_sma.update(k)
            if d is not None:
                self.d_values.append(d)
                return k, d
                
        return None
        
    def get_values(self) -> Tuple[List[float], List[float]]:
        """获取随机振荡器值"""
        return list(self.k_values), list(self.d_values)


class WilliamsR(IndicatorBase):
    """威廉指标"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.highs = deque(maxlen=period)
        self.lows = deque(maxlen=period)
        
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新威廉指标"""
        self.highs.append(high)
        self.lows.append(low)
        
        if len(self.highs) >= self.period:
            highest = max(self.highs)
            lowest = min(self.lows)
            
            if highest == lowest:
                wr = -50
            else:
                wr = (highest - close) / (highest - lowest) * -100
                
            self.values.append(wr)
            return wr
            
        return None


class CCI(IndicatorBase):
    """商品通道指标"""
    
    def __init__(self, period: int = 20):
        super().__init__(period)
        self.tp_values = deque(maxlen=period)  # 典型价格
        
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """更新CCI"""
        tp = (high + low + close) / 3  # 典型价格
        self.tp_values.append(tp)
        
        if len(self.tp_values) >= self.period:
            sma_tp = sum(self.tp_values) / len(self.tp_values)
            
            # 计算平均绝对偏差
            mad = sum(abs(tp - sma_tp) for tp in self.tp_values) / len(self.tp_values)
            
            if mad == 0:
                cci = 0
            else:
                cci = (tp - sma_tp) / (0.015 * mad)
                
            self.values.append(cci)
            return cci
            
        return None


def calculate_pivot_points(high: float, low: float, close: float) -> dict:
    """计算枢轴点"""
    pivot = (high + low + close) / 3
    
    r1 = 2 * pivot - low
    s1 = 2 * pivot - high
    
    r2 = pivot + (high - low)
    s2 = pivot - (high - low)
    
    r3 = high + 2 * (pivot - low)
    s3 = low - 2 * (high - pivot)
    
    return {
        'pivot': pivot,
        'r1': r1, 'r2': r2, 'r3': r3,
        's1': s1, 's2': s2, 's3': s3
    }


def calculate_fibonacci_retracement(high: float, low: float) -> dict:
    """计算斐波那契回调位"""
    diff = high - low
    
    return {
        '0%': high,
        '23.6%': high - 0.236 * diff,
        '38.2%': high - 0.382 * diff,
        '50%': high - 0.5 * diff,
        '61.8%': high - 0.618 * diff,
        '78.6%': high - 0.786 * diff,
        '100%': low
    }


class IndicatorManager:
    """指标管理器"""
    
    def __init__(self):
        self.indicators: Dict[str, Any] = {}
        
    def add_indicator(self, name: str, indicator: Any):
        """添加指标"""
        self.indicators[name] = indicator
        
    def remove_indicator(self, name: str):
        """移除指标"""
        if name in self.indicators:
            del self.indicators[name]
            
    def update_all(self, **kwargs):
        """更新所有指标"""
        results = {}
        
        for name, indicator in self.indicators.items():
            try:
                if hasattr(indicator, 'update'):
                    if isinstance(indicator, (KDJ, ATR, StochasticOscillator, WilliamsR, CCI)):
                        # 需要高低收价格的指标
                        if 'high' in kwargs and 'low' in kwargs and 'close' in kwargs:
                            result = indicator.update(kwargs['high'], kwargs['low'], kwargs['close'])
                            if result is not None:
                                results[name] = result
                    else:
                        # 只需要价格的指标
                        if 'price' in kwargs:
                            result = indicator.update(kwargs['price'])
                            if result is not None:
                                results[name] = result
                                
            except Exception as e:
                logger.error(f"更新指标 {name} 失败: {e}")
                
        return results
        
    def get_indicator(self, name: str) -> Optional[Any]:
        """获取指标"""
        return self.indicators.get(name)
        
    def get_all_indicators(self) -> dict:
        """获取所有指标"""
        return self.indicators.copy()
