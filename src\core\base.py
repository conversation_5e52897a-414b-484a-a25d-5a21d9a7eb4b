#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础类定义模块

定义系统中使用的基础类、枚举、常量等。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
import uuid


class EventType(Enum):
    """事件类型枚举"""
    # 系统事件
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    SYSTEM_ERROR = "system_error"
    
    # 数据事件
    MARKET_DATA = "market_data"
    TICK_DATA = "tick_data"
    KLINE_DATA = "kline_data"
    
    # 交易事件
    ORDER_SUBMIT = "order_submit"
    ORDER_CANCEL = "order_cancel"
    ORDER_FILLED = "order_filled"
    ORDER_REJECTED = "order_rejected"
    
    # 策略事件
    STRATEGY_START = "strategy_start"
    STRATEGY_STOP = "strategy_stop"
    STRATEGY_ERROR = "strategy_error"
    
    # 风控事件
    RISK_CHECK = "risk_check"
    RISK_ALERT = "risk_alert"
    
    # 定时器事件
    TIMER = "timer"


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"          # 待提交
    SUBMITTED = "submitted"      # 已提交
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"           # 全部成交
    CANCELLED = "cancelled"     # 已撤销
    REJECTED = "rejected"       # 已拒绝
    ERROR = "error"            # 错误


class OrderType(Enum):
    """订单类型枚举"""
    LIMIT = "limit"            # 限价单
    MARKET = "market"          # 市价单
    STOP = "stop"              # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


class Direction(Enum):
    """交易方向枚举"""
    LONG = "long"              # 多头
    SHORT = "short"            # 空头


class Offset(Enum):
    """开平仓枚举"""
    OPEN = "open"              # 开仓
    CLOSE = "close"            # 平仓
    CLOSE_TODAY = "close_today"    # 平今
    CLOSE_YESTERDAY = "close_yesterday"  # 平昨


@dataclass
class Event:
    """事件类"""
    type: EventType
    data: Any = None
    timestamp: datetime = None
    source: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class TickData:
    """Tick数据类"""
    symbol: str
    exchange: str
    datetime: datetime
    name: str = ""
    volume: float = 0.0
    turnover: float = 0.0
    open_interest: float = 0.0
    last_price: float = 0.0
    last_volume: float = 0.0
    limit_up: float = 0.0
    limit_down: float = 0.0
    
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    pre_close: float = 0.0
    
    bid_price_1: float = 0.0
    bid_price_2: float = 0.0
    bid_price_3: float = 0.0
    bid_price_4: float = 0.0
    bid_price_5: float = 0.0
    
    ask_price_1: float = 0.0
    ask_price_2: float = 0.0
    ask_price_3: float = 0.0
    ask_price_4: float = 0.0
    ask_price_5: float = 0.0
    
    bid_volume_1: float = 0.0
    bid_volume_2: float = 0.0
    bid_volume_3: float = 0.0
    bid_volume_4: float = 0.0
    bid_volume_5: float = 0.0
    
    ask_volume_1: float = 0.0
    ask_volume_2: float = 0.0
    ask_volume_3: float = 0.0
    ask_volume_4: float = 0.0
    ask_volume_5: float = 0.0
    
    localtime: datetime = None
    
    def __post_init__(self):
        if self.localtime is None:
            self.localtime = datetime.now()


@dataclass
class BarData:
    """K线数据类"""
    symbol: str
    exchange: str
    datetime: datetime
    interval: str
    volume: float
    turnover: float
    open_interest: float
    open_price: float
    high_price: float
    low_price: float
    close_price: float


@dataclass
class OrderData:
    """订单数据类"""
    orderid: str
    symbol: str
    exchange: str
    type: OrderType
    direction: Direction
    offset: Offset
    price: float
    volume: float
    traded: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    datetime: datetime = None
    
    def __post_init__(self):
        if self.datetime is None:
            self.datetime = datetime.now()
        if not self.orderid:
            self.orderid = str(uuid.uuid4())


@dataclass
class TradeData:
    """成交数据类"""
    tradeid: str
    orderid: str
    symbol: str
    exchange: str
    direction: Direction
    offset: Offset
    price: float
    volume: float
    datetime: datetime = None
    
    def __post_init__(self):
        if self.datetime is None:
            self.datetime = datetime.now()
        if not self.tradeid:
            self.tradeid = str(uuid.uuid4())


@dataclass
class PositionData:
    """持仓数据类"""
    symbol: str
    exchange: str
    direction: Direction
    volume: float
    frozen: float = 0.0
    price: float = 0.0
    pnl: float = 0.0
    yd_volume: float = 0.0


@dataclass
class AccountData:
    """账户数据类"""
    accountid: str
    balance: float = 0.0
    frozen: float = 0.0
    available: float = 0.0
    
    def __post_init__(self):
        if self.available == 0.0:
            self.available = self.balance - self.frozen


class BaseObject:
    """基础对象类"""
    
    def __init__(self):
        self.name = self.__class__.__name__
        
    def write_log(self, msg: str, level: str = "INFO"):
        """写日志"""
        print(f"[{level}] {self.name}: {msg}")


class BaseEngine(BaseObject):
    """基础引擎类"""
    
    def __init__(self, main_engine=None, event_engine=None):
        super().__init__()
        self.main_engine = main_engine
        self.event_engine = event_engine
        
    @abstractmethod
    def start(self):
        """启动引擎"""
        pass
        
    @abstractmethod
    def stop(self):
        """停止引擎"""
        pass
