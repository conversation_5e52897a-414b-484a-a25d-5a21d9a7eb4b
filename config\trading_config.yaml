# 交易配置文件
trading:
  # 交易模式
  mode: "simulation"  # simulation, live
  
  # 交易时段
  trading_sessions:
    stock:
      morning: ["09:30:00", "11:30:00"]
      afternoon: ["13:00:00", "15:00:00"]
    futures:
      day: ["09:00:00", "15:00:00"]
      night: ["21:00:00", "02:30:00"]
      
  # 交易费用设置
  fees:
    stock:
      commission_rate: 0.0003  # 佣金费率
      min_commission: 5.0      # 最小佣金
      stamp_tax_rate: 0.001    # 印花税（卖出）
      transfer_fee_rate: 0.00002  # 过户费
    futures:
      commission_rate: 0.0001
      min_commission: 2.0
      
  # 滑点设置
  slippage:
    stock:
      rate: 0.001  # 滑点率
      fixed: 0.0   # 固定滑点
    futures:
      rate: 0.0005
      fixed: 0.0
      
# 风险管理配置
risk_management:
  # 全局风险控制
  global:
    max_position_ratio: 0.95    # 最大仓位比例
    max_single_position: 0.20   # 单个品种最大仓位
    max_daily_loss: 0.05        # 单日最大亏损比例
    max_drawdown: 0.15          # 最大回撤比例
    
  # 策略级风险控制
  strategy:
    max_position_ratio: 0.30    # 单策略最大仓位
    max_daily_trades: 100       # 单日最大交易次数
    min_interval_seconds: 1     # 最小交易间隔
    
  # 品种级风险控制
  instrument:
    max_position_ratio: 0.10    # 单品种最大仓位
    max_order_size: 1000        # 单笔最大下单量
    
# 资金管理配置
capital_management:
  initial_capital: 1000000.0    # 初始资金
  reserve_ratio: 0.05           # 资金保留比例
  
  # 仓位管理
  position_sizing:
    method: "fixed_ratio"       # fixed_ratio, kelly, volatility
    fixed_ratio: 0.02           # 固定比例
    kelly_lookback: 252         # 凯利公式回望期
    volatility_target: 0.15     # 目标波动率
    
# 账户配置
accounts:
  # 模拟账户
  simulation:
    account_id: "SIM_001"
    initial_balance: 1000000.0
    currency: "CNY"
    
  # 实盘账户（示例）
  live:
    account_id: ""
    broker: "miniqmt"
    username: ""
    password: ""
    server: ""
    
# 交易品种配置
instruments:
  # 股票配置
  stocks:
    enabled: true
    markets: ["SH", "SZ"]
    min_price: 0.01
    price_tick: 0.01
    lot_size: 100
    
  # 期货配置
  futures:
    enabled: true
    exchanges: ["SHFE", "DCE", "CZCE", "INE"]
    margin_ratio: 0.10
    
  # 期权配置
  options:
    enabled: false
    
# 订单管理配置
order_management:
  # 订单类型
  order_types: ["LIMIT", "MARKET", "STOP", "STOP_LIMIT"]
  
  # 订单有效期
  time_in_force: ["GTC", "IOC", "FOK", "DAY"]
  
  # 订单路由
  routing:
    default_exchange: "AUTO"
    smart_routing: true
    
  # 订单监控
  monitoring:
    timeout_seconds: 300        # 订单超时时间
    max_pending_orders: 50      # 最大挂单数量
    
# 策略配置
strategies:
  # 策略运行配置
  execution:
    max_concurrent: 10          # 最大并发策略数
    heartbeat_interval: 30      # 心跳间隔（秒）
    
  # 策略参数
  parameters:
    default_lookback: 20        # 默认回望期
    default_frequency: "1m"     # 默认频率
