#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块

提供数据库连接、数据存储、查询等功能，支持SQLite和MongoDB。
"""

import os
import sqlite3
from datetime import datetime, date
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Float, DateTime, Boolean
from sqlalchemy.orm import sessionmaker
from loguru import logger

try:
    import pymongo
    from pymongo import MongoClient
    MONGO_AVAILABLE = True
except ImportError:
    MONGO_AVAILABLE = False
    logger.warning("pymongo未安装，MongoDB功能不可用")

from ..core.base import BaseEngine, TickData, BarData, OrderData, TradeData, PositionData


class DatabaseManager(BaseEngine):
    """数据库管理器"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化数据库管理器"""
        super().__init__(main_engine, event_engine)
        
        # 数据库连接
        self.sqlite_engine = None
        self.sqlite_session = None
        self.mongo_client = None
        self.mongo_db = None
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('system', {}).get('database', {})
            
        # SQLite配置
        self.sqlite_config = self.config.get('sqlite', {})
        self.sqlite_path = self.sqlite_config.get('path', 'data/database.db')
        
        # MongoDB配置
        self.mongo_config = self.config.get('mongodb', {})
        
        logger.info("数据库管理器初始化完成")
        
    def start(self):
        """启动数据库管理器"""
        logger.info("启动数据库管理器...")
        
        # 初始化SQLite
        self._init_sqlite()
        
        # 初始化MongoDB（如果可用）
        if MONGO_AVAILABLE and self.mongo_config.get('host'):
            self._init_mongodb()
            
        logger.info("数据库管理器启动完成")
        
    def stop(self):
        """停止数据库管理器"""
        logger.info("停止数据库管理器...")
        
        # 关闭SQLite连接
        if self.sqlite_session:
            self.sqlite_session.close()
            
        if self.sqlite_engine:
            self.sqlite_engine.dispose()
            
        # 关闭MongoDB连接
        if self.mongo_client:
            self.mongo_client.close()
            
        logger.info("数据库管理器已停止")
        
    def _init_sqlite(self):
        """初始化SQLite数据库"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.sqlite_path), exist_ok=True)
            
            # 创建引擎
            self.sqlite_engine = create_engine(
                f'sqlite:///{self.sqlite_path}',
                echo=self.sqlite_config.get('echo', False),
                pool_size=self.sqlite_config.get('pool_size', 10),
                max_overflow=self.sqlite_config.get('max_overflow', 20)
            )
            
            # 创建会话
            Session = sessionmaker(bind=self.sqlite_engine)
            self.sqlite_session = Session()
            
            # 创建表结构
            self._create_tables()
            
            logger.info(f"SQLite数据库初始化完成: {self.sqlite_path}")
            
        except Exception as e:
            logger.error(f"SQLite数据库初始化失败: {e}")
            
    def _init_mongodb(self):
        """初始化MongoDB数据库"""
        try:
            host = self.mongo_config.get('host', 'localhost')
            port = self.mongo_config.get('port', 27017)
            database = self.mongo_config.get('database', 'wtpy_quant')
            username = self.mongo_config.get('username', '')
            password = self.mongo_config.get('password', '')
            
            # 构建连接字符串
            if username and password:
                uri = f"mongodb://{username}:{password}@{host}:{port}/{database}"
            else:
                uri = f"mongodb://{host}:{port}"
                
            # 创建客户端
            self.mongo_client = MongoClient(uri)
            self.mongo_db = self.mongo_client[database]
            
            # 测试连接
            self.mongo_client.admin.command('ping')
            
            logger.info(f"MongoDB数据库初始化完成: {host}:{port}/{database}")
            
        except Exception as e:
            logger.error(f"MongoDB数据库初始化失败: {e}")
            self.mongo_client = None
            self.mongo_db = None
            
    def _create_tables(self):
        """创建数据表"""
        metadata = MetaData()
        
        # Tick数据表
        tick_table = Table(
            'tick_data', metadata,
            Column('id', Integer, primary_key=True),
            Column('symbol', String(20), nullable=False),
            Column('exchange', String(10), nullable=False),
            Column('datetime', DateTime, nullable=False),
            Column('last_price', Float),
            Column('volume', Float),
            Column('turnover', Float),
            Column('open_interest', Float),
            Column('open_price', Float),
            Column('high_price', Float),
            Column('low_price', Float),
            Column('pre_close', Float),
            Column('bid_price_1', Float),
            Column('ask_price_1', Float),
            Column('bid_volume_1', Float),
            Column('ask_volume_1', Float),
            Column('localtime', DateTime),
        )
        
        # K线数据表
        bar_table = Table(
            'bar_data', metadata,
            Column('id', Integer, primary_key=True),
            Column('symbol', String(20), nullable=False),
            Column('exchange', String(10), nullable=False),
            Column('datetime', DateTime, nullable=False),
            Column('interval', String(10), nullable=False),
            Column('open_price', Float),
            Column('high_price', Float),
            Column('low_price', Float),
            Column('close_price', Float),
            Column('volume', Float),
            Column('turnover', Float),
            Column('open_interest', Float),
        )
        
        # 订单数据表
        order_table = Table(
            'order_data', metadata,
            Column('id', Integer, primary_key=True),
            Column('orderid', String(50), nullable=False, unique=True),
            Column('symbol', String(20), nullable=False),
            Column('exchange', String(10), nullable=False),
            Column('type', String(20), nullable=False),
            Column('direction', String(10), nullable=False),
            Column('offset', String(20), nullable=False),
            Column('price', Float),
            Column('volume', Float),
            Column('traded', Float),
            Column('status', String(20)),
            Column('datetime', DateTime),
        )
        
        # 成交数据表
        trade_table = Table(
            'trade_data', metadata,
            Column('id', Integer, primary_key=True),
            Column('tradeid', String(50), nullable=False, unique=True),
            Column('orderid', String(50), nullable=False),
            Column('symbol', String(20), nullable=False),
            Column('exchange', String(10), nullable=False),
            Column('direction', String(10), nullable=False),
            Column('offset', String(20), nullable=False),
            Column('price', Float),
            Column('volume', Float),
            Column('datetime', DateTime),
        )
        
        # 持仓数据表
        position_table = Table(
            'position_data', metadata,
            Column('id', Integer, primary_key=True),
            Column('symbol', String(20), nullable=False),
            Column('exchange', String(10), nullable=False),
            Column('direction', String(10), nullable=False),
            Column('volume', Float),
            Column('frozen', Float),
            Column('price', Float),
            Column('pnl', Float),
            Column('yd_volume', Float),
            Column('update_time', DateTime),
        )
        
        # 创建所有表
        metadata.create_all(self.sqlite_engine)
        logger.info("数据表创建完成")
        
    def save_tick_data(self, tick_data: TickData) -> bool:
        """保存Tick数据"""
        try:
            # SQLite存储
            if self.sqlite_engine:
                tick_dict = {
                    'symbol': tick_data.symbol,
                    'exchange': tick_data.exchange,
                    'datetime': tick_data.datetime,
                    'last_price': tick_data.last_price,
                    'volume': tick_data.volume,
                    'turnover': tick_data.turnover,
                    'open_interest': tick_data.open_interest,
                    'open_price': tick_data.open_price,
                    'high_price': tick_data.high_price,
                    'low_price': tick_data.low_price,
                    'pre_close': tick_data.pre_close,
                    'bid_price_1': tick_data.bid_price_1,
                    'ask_price_1': tick_data.ask_price_1,
                    'bid_volume_1': tick_data.bid_volume_1,
                    'ask_volume_1': tick_data.ask_volume_1,
                    'localtime': tick_data.localtime,
                }
                
                df = pd.DataFrame([tick_dict])
                df.to_sql('tick_data', self.sqlite_engine, if_exists='append', index=False)
                
            # MongoDB存储
            if self.mongo_db:
                tick_dict = tick_data.__dict__.copy()
                # 转换datetime为MongoDB兼容格式
                for key, value in tick_dict.items():
                    if isinstance(value, datetime):
                        tick_dict[key] = value
                        
                self.mongo_db.tick_data.insert_one(tick_dict)
                
            return True
            
        except Exception as e:
            logger.error(f"保存Tick数据失败: {e}")
            return False
            
    def save_bar_data(self, bar_data: BarData) -> bool:
        """保存K线数据"""
        try:
            # SQLite存储
            if self.sqlite_engine:
                bar_dict = {
                    'symbol': bar_data.symbol,
                    'exchange': bar_data.exchange,
                    'datetime': bar_data.datetime,
                    'interval': bar_data.interval,
                    'open_price': bar_data.open_price,
                    'high_price': bar_data.high_price,
                    'low_price': bar_data.low_price,
                    'close_price': bar_data.close_price,
                    'volume': bar_data.volume,
                    'turnover': bar_data.turnover,
                    'open_interest': bar_data.open_interest,
                }
                
                df = pd.DataFrame([bar_dict])
                df.to_sql('bar_data', self.sqlite_engine, if_exists='append', index=False)
                
            # MongoDB存储
            if self.mongo_db:
                bar_dict = bar_data.__dict__.copy()
                self.mongo_db.bar_data.insert_one(bar_dict)
                
            return True
            
        except Exception as e:
            logger.error(f"保存K线数据失败: {e}")
            return False
            
    def get_bar_data(self, symbol: str, exchange: str, interval: str,
                     start: datetime = None, end: datetime = None,
                     limit: int = None) -> List[BarData]:
        """查询K线数据"""
        try:
            # 构建查询条件
            conditions = [
                f"symbol = '{symbol}'",
                f"exchange = '{exchange}'",
                f"interval = '{interval}'"
            ]
            
            if start:
                conditions.append(f"datetime >= '{start}'")
            if end:
                conditions.append(f"datetime <= '{end}'")
                
            where_clause = " AND ".join(conditions)
            
            # 构建SQL查询
            sql = f"SELECT * FROM bar_data WHERE {where_clause} ORDER BY datetime"
            if limit:
                sql += f" LIMIT {limit}"
                
            # 执行查询
            df = pd.read_sql(sql, self.sqlite_engine)
            
            # 转换为BarData对象
            bar_list = []
            for _, row in df.iterrows():
                bar = BarData(
                    symbol=row['symbol'],
                    exchange=row['exchange'],
                    datetime=pd.to_datetime(row['datetime']),
                    interval=row['interval'],
                    open_price=row['open_price'],
                    high_price=row['high_price'],
                    low_price=row['low_price'],
                    close_price=row['close_price'],
                    volume=row['volume'],
                    turnover=row['turnover'],
                    open_interest=row['open_interest'],
                )
                bar_list.append(bar)
                
            return bar_list
            
        except Exception as e:
            logger.error(f"查询K线数据失败: {e}")
            return []
            
    def get_tick_data(self, symbol: str, exchange: str,
                      start: datetime = None, end: datetime = None,
                      limit: int = None) -> List[TickData]:
        """查询Tick数据"""
        try:
            # 构建查询条件
            conditions = [
                f"symbol = '{symbol}'",
                f"exchange = '{exchange}'"
            ]
            
            if start:
                conditions.append(f"datetime >= '{start}'")
            if end:
                conditions.append(f"datetime <= '{end}'")
                
            where_clause = " AND ".join(conditions)
            
            # 构建SQL查询
            sql = f"SELECT * FROM tick_data WHERE {where_clause} ORDER BY datetime"
            if limit:
                sql += f" LIMIT {limit}"
                
            # 执行查询
            df = pd.read_sql(sql, self.sqlite_engine)
            
            # 转换为TickData对象
            tick_list = []
            for _, row in df.iterrows():
                tick = TickData(
                    symbol=row['symbol'],
                    exchange=row['exchange'],
                    datetime=pd.to_datetime(row['datetime']),
                    last_price=row['last_price'],
                    volume=row['volume'],
                    turnover=row['turnover'],
                    open_interest=row['open_interest'],
                    open_price=row['open_price'],
                    high_price=row['high_price'],
                    low_price=row['low_price'],
                    pre_close=row['pre_close'],
                    bid_price_1=row['bid_price_1'],
                    ask_price_1=row['ask_price_1'],
                    bid_volume_1=row['bid_volume_1'],
                    ask_volume_1=row['ask_volume_1'],
                    localtime=pd.to_datetime(row['localtime']) if row['localtime'] else None,
                )
                tick_list.append(tick)
                
            return tick_list
            
        except Exception as e:
            logger.error(f"查询Tick数据失败: {e}")
            return []
            
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return self.sqlite_engine is not None
