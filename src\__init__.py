#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WtPy量化交易系统

基于wtpy框架的完整量化交易系统，提供实盘交易、回测引擎、GUI管理界面等功能。
"""

__version__ = "1.0.0"
__author__ = "Quant Team"
__email__ = "<EMAIL>"

# 导入核心模块
from .core.engine import MainEngine
from .core.event_engine import EventEngine
from .core.base import BaseObject, Event

# 导入数据模块
from .data.data_manager import DataManager
from .data.miniqmt_api import MiniqmtApi

# 导入交易模块
from .trading.trader import Trader
from .trading.portfolio import Portfolio
from .trading.risk_manager import RiskManager

# 导入回测模块
from .backtest.backtest_engine import BacktestEngine
from .backtest.analyzer import Analyzer

# 导入策略模块
from .strategy.base_strategy import BaseStrategy
from .strategy.template import StrategyTemplate

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    
    # 核心模块
    "MainEngine",
    "EventEngine",
    "BaseObject",
    "Event",
    
    # 数据模块
    "DataManager",
    "MiniqmtApi",
    
    # 交易模块
    "Trader",
    "Portfolio",
    "RiskManager",
    
    # 回测模块
    "BacktestEngine",
    "Analyzer",
    
    # 策略模块
    "BaseStrategy",
    "StrategyTemplate",
]
