#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略模块测试

测试策略基类、管理器、技术指标等功能。
"""

import sys
import unittest
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.core.base import TickData, BarData, Direction, Offset
from src.strategy.base import BaseStrategy, StrategyParameter
from src.strategy.manager import StrategyManager
from src.strategy.indicators import SMA, EMA, RSI, MACD, BollingerBands


class TestStrategy(BaseStrategy):
    """测试策略类"""
    
    def __init__(self, strategy_name: str, symbols: list):
        super().__init__(strategy_name, symbols)
        
        # 添加参数
        self.add_parameter("test_param", 10, "测试参数", int, 1, 100)
        
        # 计数器
        self.tick_count = 0
        self.bar_count = 0
        
    def on_start(self):
        self.write_log("测试策略启动")
        
    def on_stop(self):
        self.write_log("测试策略停止")
        
    def on_tick_data(self, tick: TickData):
        self.tick_count += 1
        
    def on_bar_data(self, bar: BarData):
        self.bar_count += 1


class TestStrategyParameter(unittest.TestCase):
    """策略参数测试"""
    
    def test_init(self):
        """测试初始化"""
        param = StrategyParameter("test", 10, "测试参数", int, 1, 100)
        
        self.assertEqual(param.name, "test")
        self.assertEqual(param.value, 10)
        self.assertEqual(param.description, "测试参数")
        self.assertEqual(param.param_type, int)
        self.assertEqual(param.min_value, 1)
        self.assertEqual(param.max_value, 100)
        
    def test_validate(self):
        """测试参数验证"""
        param = StrategyParameter("test", 10, "测试参数", int, 1, 100)
        
        # 有效值
        self.assertTrue(param.validate(50))
        self.assertTrue(param.validate(1))
        self.assertTrue(param.validate(100))
        
        # 无效值
        self.assertFalse(param.validate(0))
        self.assertFalse(param.validate(101))
        self.assertFalse(param.validate("invalid"))
        
    def test_set_value(self):
        """测试设置值"""
        param = StrategyParameter("test", 10, "测试参数", int, 1, 100)
        
        # 设置有效值
        self.assertTrue(param.set_value(50))
        self.assertEqual(param.value, 50)
        
        # 设置无效值
        self.assertFalse(param.set_value(0))
        self.assertEqual(param.value, 50)  # 值不应该改变


class TestBaseStrategy(unittest.TestCase):
    """策略基类测试"""
    
    def setUp(self):
        """测试前准备"""
        self.strategy = TestStrategy("TestStrategy", ["000001.SZ", "600000.SH"])
        
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.strategy.strategy_name, "TestStrategy")
        self.assertEqual(self.strategy.symbols, ["000001.SZ", "600000.SH"])
        self.assertFalse(self.strategy.active)
        self.assertFalse(self.strategy.trading)
        
    def test_parameters(self):
        """测试参数管理"""
        # 检查参数
        self.assertIn("test_param", self.strategy.parameters)
        self.assertEqual(self.strategy.get_parameter("test_param"), 10)
        
        # 设置参数
        self.assertTrue(self.strategy.set_parameter("test_param", 20))
        self.assertEqual(self.strategy.get_parameter("test_param"), 20)
        
        # 获取参数信息
        info = self.strategy.get_parameters_info()
        self.assertIn("test_param", info)
        self.assertEqual(info["test_param"]["value"], 20)
        
    def test_start_stop(self):
        """测试启动停止"""
        # 启动策略
        self.strategy.start()
        self.assertTrue(self.strategy.active)
        self.assertTrue(self.strategy.trading)
        
        # 暂停交易
        self.strategy.pause_trading()
        self.assertTrue(self.strategy.active)
        self.assertFalse(self.strategy.trading)
        
        # 恢复交易
        self.strategy.resume_trading()
        self.assertTrue(self.strategy.active)
        self.assertTrue(self.strategy.trading)
        
        # 停止策略
        self.strategy.stop()
        self.assertFalse(self.strategy.active)
        self.assertFalse(self.strategy.trading)
        
    def test_data_handling(self):
        """测试数据处理"""
        self.strategy.start()
        
        # 创建测试数据
        tick = TickData(
            symbol="000001.SZ",
            exchange="SZ",
            datetime=datetime.now(),
            last_price=10.50,
            volume=1000,
            turnover=10500.0
        )
        
        bar = BarData(
            symbol="000001.SZ",
            exchange="SZ",
            datetime=datetime.now(),
            interval="1m",
            open_price=10.40,
            high_price=10.60,
            low_price=10.30,
            close_price=10.50,
            volume=1000,
            turnover=10500.0
        )
        
        # 处理数据
        initial_tick_count = self.strategy.tick_count
        initial_bar_count = self.strategy.bar_count
        
        self.strategy.on_tick(tick)
        self.strategy.on_bar(bar)
        
        self.assertEqual(self.strategy.tick_count, initial_tick_count + 1)
        self.assertEqual(self.strategy.bar_count, initial_bar_count + 1)
        
        # 检查数据存储
        self.assertEqual(len(self.strategy.ticks["000001.SZ"]), 1)
        self.assertEqual(len(self.strategy.bars["000001.SZ"]), 1)
        
    def test_statistics(self):
        """测试统计信息"""
        stats = self.strategy.get_statistics()
        
        self.assertEqual(stats["strategy_name"], "TestStrategy")
        self.assertEqual(stats["symbols"], ["000001.SZ", "600000.SH"])
        self.assertIn("parameters", stats)


class TestStrategyManager(unittest.TestCase):
    """策略管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        self.manager = StrategyManager(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'manager'):
            self.manager.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager)
        self.assertEqual(len(self.manager.strategies), 0)
        self.assertEqual(len(self.manager.strategy_classes), 0)
        
    def test_register_strategy_class(self):
        """测试注册策略类"""
        self.manager.register_strategy_class(TestStrategy)
        
        self.assertIn("TestStrategy", self.manager.strategy_classes)
        self.assertEqual(self.manager.strategy_classes["TestStrategy"], TestStrategy)
        
    def test_create_strategy(self):
        """测试创建策略"""
        # 先注册策略类
        self.manager.register_strategy_class(TestStrategy)
        
        # 创建策略
        success = self.manager.create_strategy(
            "TestStrategy", 
            "test_instance", 
            ["000001.SZ"],
            {"test_param": 20}
        )
        
        self.assertTrue(success)
        self.assertIn("test_instance", self.manager.strategies)
        
        # 检查策略配置
        strategy = self.manager.get_strategy("test_instance")
        self.assertIsNotNone(strategy)
        self.assertEqual(strategy.get_parameter("test_param"), 20)
        
    def test_start_stop_strategy(self):
        """测试启动停止策略"""
        # 创建策略
        self.manager.register_strategy_class(TestStrategy)
        self.manager.create_strategy("TestStrategy", "test_instance", ["000001.SZ"])
        
        # 启动策略
        success = self.manager.start_strategy("test_instance")
        self.assertTrue(success)
        
        strategy = self.manager.get_strategy("test_instance")
        self.assertTrue(strategy.active)
        
        # 停止策略
        success = self.manager.stop_strategy("test_instance")
        self.assertTrue(success)
        self.assertFalse(strategy.active)
        
    def test_remove_strategy(self):
        """测试移除策略"""
        # 创建策略
        self.manager.register_strategy_class(TestStrategy)
        self.manager.create_strategy("TestStrategy", "test_instance", ["000001.SZ"])
        
        # 移除策略
        success = self.manager.remove_strategy("test_instance")
        self.assertTrue(success)
        self.assertNotIn("test_instance", self.manager.strategies)
        
    def test_get_strategy_info(self):
        """测试获取策略信息"""
        # 创建策略
        self.manager.register_strategy_class(TestStrategy)
        self.manager.create_strategy("TestStrategy", "test_instance", ["000001.SZ"])
        
        # 获取策略信息
        info = self.manager.get_strategy_info("test_instance")
        self.assertIsNotNone(info)
        self.assertEqual(info["name"], "test_instance")
        self.assertEqual(info["class_name"], "TestStrategy")
        self.assertEqual(info["symbols"], ["000001.SZ"])


class TestIndicators(unittest.TestCase):
    """技术指标测试"""
    
    def test_sma(self):
        """测试简单移动平均"""
        sma = SMA(5)
        
        # 添加数据
        prices = [10, 11, 12, 13, 14, 15, 16]
        results = []
        
        for price in prices:
            result = sma.update(price)
            if result is not None:
                results.append(result)
                
        # 检查结果
        self.assertEqual(len(results), 3)  # 前5个数据不足，后3个有结果
        self.assertAlmostEqual(results[0], 12.0)  # (10+11+12+13+14)/5
        self.assertAlmostEqual(results[1], 13.0)  # (11+12+13+14+15)/5
        self.assertAlmostEqual(results[2], 14.0)  # (12+13+14+15+16)/5
        
    def test_ema(self):
        """测试指数移动平均"""
        ema = EMA(5)
        
        # 添加数据
        prices = [10, 11, 12, 13, 14, 15, 16]
        results = []
        
        for price in prices:
            result = ema.update(price)
            if result is not None:
                results.append(result)
                
        # 检查结果
        self.assertGreater(len(results), 0)
        self.assertIsInstance(results[0], float)
        
    def test_rsi(self):
        """测试RSI指标"""
        rsi = RSI(14)
        
        # 添加价格数据（模拟上涨趋势）
        prices = [10 + i * 0.1 for i in range(20)]
        results = []
        
        for price in prices:
            result = rsi.update(price)
            if result is not None:
                results.append(result)
                
        # 检查结果
        self.assertGreater(len(results), 0)
        # RSI应该在0-100之间
        for rsi_value in results:
            self.assertGreaterEqual(rsi_value, 0)
            self.assertLessEqual(rsi_value, 100)
            
    def test_macd(self):
        """测试MACD指标"""
        macd = MACD(12, 26, 9)
        
        # 添加价格数据
        prices = [10 + i * 0.1 for i in range(50)]
        results = []
        
        for price in prices:
            result = macd.update(price)
            if result is not None:
                results.append(result)
                
        # 检查结果
        self.assertGreater(len(results), 0)
        
        # 检查返回值格式
        macd_line, signal_line, histogram = results[-1]
        self.assertIsInstance(macd_line, float)
        self.assertIsInstance(signal_line, float)
        self.assertIsInstance(histogram, float)
        
    def test_bollinger_bands(self):
        """测试布林带"""
        bb = BollingerBands(20, 2.0)
        
        # 添加价格数据
        prices = [10 + i * 0.05 for i in range(30)]
        results = []
        
        for price in prices:
            result = bb.update(price)
            if result is not None:
                results.append(result)
                
        # 检查结果
        self.assertGreater(len(results), 0)
        
        # 检查返回值格式
        upper, middle, lower = results[-1]
        self.assertIsInstance(upper, float)
        self.assertIsInstance(middle, float)
        self.assertIsInstance(lower, float)
        
        # 检查布林带关系
        self.assertGreater(upper, middle)
        self.assertGreater(middle, lower)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
