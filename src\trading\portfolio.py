#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投资组合管理模块

提供投资组合管理、资金分配、绩效统计等功能。
"""

from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import pandas as pd

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, OrderData, TradeData, PositionData, AccountData,
    Direction, Offset
)


class Portfolio(BaseEngine):
    """投资组合管理器"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化投资组合管理器"""
        super().__init__(main_engine, event_engine)
        
        # 组合数据
        self._positions: Dict[str, PositionData] = {}
        self._trades: List[TradeData] = []
        self._daily_pnl: Dict[date, float] = {}
        self._daily_balance: Dict[date, float] = {}
        
        # 统计数据
        self._total_pnl = 0.0
        self._total_commission = 0.0
        self._total_trades = 0
        self._win_trades = 0
        self._lose_trades = 0
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('trading', {})
            
        # 资金管理配置
        self.capital_config = self.config.get('capital_management', {})
        self.initial_capital = self.capital_config.get('initial_capital', 1000000.0)
        self.current_capital = self.initial_capital
        
        # 注册事件处理器
        if event_engine:
            event_engine.register(EventType.ORDER_FILLED, self._on_trade)
            
        logger.info("投资组合管理器初始化完成")
        
    def start(self):
        """启动投资组合管理器"""
        logger.info("启动投资组合管理器...")
        
        # 初始化资金记录
        today = date.today()
        self._daily_balance[today] = self.initial_capital
        self._daily_pnl[today] = 0.0
        
        logger.info("投资组合管理器启动完成")
        
    def stop(self):
        """停止投资组合管理器"""
        logger.info("停止投资组合管理器...")
        
        # 输出统计信息
        self._print_statistics()
        
        logger.info("投资组合管理器已停止")
        
    def _on_trade(self, event: Event):
        """处理成交事件"""
        trade: TradeData = event.data
        if not trade:
            return
            
        # 记录成交
        self._trades.append(trade)
        self._total_trades += 1
        
        # 更新持仓
        self._update_position(trade)
        
        # 计算手续费
        commission = self._calculate_commission(trade)
        self._total_commission += commission
        
        # 更新统计
        self._update_statistics(trade)
        
        logger.info(f"组合成交记录: {trade.symbol} {trade.direction.value} "
                   f"{trade.volume}@{trade.price} 手续费:{commission:.2f}")
                   
    def _update_position(self, trade: TradeData):
        """更新持仓"""
        position_key = f"{trade.symbol}.{trade.exchange}"
        
        if position_key in self._positions:
            position = self._positions[position_key]
            
            if trade.direction == Direction.LONG:
                if trade.offset == Offset.OPEN:
                    # 买入开仓
                    total_volume = position.volume + trade.volume
                    if total_volume > 0:
                        total_cost = position.volume * position.price + trade.volume * trade.price
                        position.price = total_cost / total_volume
                        position.volume = total_volume
                        position.direction = Direction.LONG
                elif trade.offset == Offset.CLOSE:
                    # 买入平仓（平空头）
                    if position.direction == Direction.SHORT:
                        position.volume -= trade.volume
                        if position.volume <= 0:
                            # 计算平仓盈亏
                            pnl = (position.price - trade.price) * abs(position.volume)
                            self._total_pnl += pnl
                            if position.volume == 0:
                                del self._positions[position_key]
                            
            else:  # SHORT
                if trade.offset == Offset.OPEN:
                    # 卖出开仓
                    if position.direction == Direction.LONG:
                        # 先平多头
                        if position.volume >= trade.volume:
                            # 计算平仓盈亏
                            pnl = (trade.price - position.price) * trade.volume
                            self._total_pnl += pnl
                            position.volume -= trade.volume
                            if position.volume == 0:
                                del self._positions[position_key]
                        else:
                            # 平完多头后开空头
                            pnl = (trade.price - position.price) * position.volume
                            self._total_pnl += pnl
                            remaining_volume = trade.volume - position.volume
                            position.volume = remaining_volume
                            position.price = trade.price
                            position.direction = Direction.SHORT
                    else:
                        # 增加空头
                        total_volume = position.volume + trade.volume
                        if total_volume > 0:
                            total_cost = position.volume * position.price + trade.volume * trade.price
                            position.price = total_cost / total_volume
                            position.volume = total_volume
                            position.direction = Direction.SHORT
                            
                elif trade.offset == Offset.CLOSE:
                    # 卖出平仓（平多头）
                    if position.direction == Direction.LONG:
                        # 计算平仓盈亏
                        pnl = (trade.price - position.price) * trade.volume
                        self._total_pnl += pnl
                        position.volume -= trade.volume
                        if position.volume <= 0:
                            if position.volume == 0:
                                del self._positions[position_key]
                                
        else:
            # 新建持仓
            if trade.offset == Offset.OPEN:
                position = PositionData(
                    symbol=trade.symbol,
                    exchange=trade.exchange,
                    direction=trade.direction,
                    volume=trade.volume,
                    price=trade.price
                )
                self._positions[position_key] = position
                
    def _calculate_commission(self, trade: TradeData) -> float:
        """计算手续费"""
        # 获取费用配置
        fees_config = self.config.get('trading', {}).get('fees', {})
        
        # 根据品种类型计算手续费
        if trade.exchange in ['SH', 'SZ']:
            # 股票手续费
            stock_fees = fees_config.get('stock', {})
            commission_rate = stock_fees.get('commission_rate', 0.0003)
            min_commission = stock_fees.get('min_commission', 5.0)
            
            commission = trade.price * trade.volume * commission_rate
            commission = max(commission, min_commission)
            
            # 印花税（卖出时收取）
            if trade.direction == Direction.SHORT:
                stamp_tax_rate = stock_fees.get('stamp_tax_rate', 0.001)
                commission += trade.price * trade.volume * stamp_tax_rate
                
            # 过户费
            transfer_fee_rate = stock_fees.get('transfer_fee_rate', 0.00002)
            commission += trade.price * trade.volume * transfer_fee_rate
            
        else:
            # 期货手续费
            futures_fees = fees_config.get('futures', {})
            commission_rate = futures_fees.get('commission_rate', 0.0001)
            min_commission = futures_fees.get('min_commission', 2.0)
            
            commission = trade.price * trade.volume * commission_rate
            commission = max(commission, min_commission)
            
        return commission
        
    def _update_statistics(self, trade: TradeData):
        """更新统计数据"""
        # 更新每日盈亏
        today = date.today()
        if today not in self._daily_pnl:
            self._daily_pnl[today] = 0.0
            
        # 更新资金
        self.current_capital = self.initial_capital + self._total_pnl - self._total_commission
        self._daily_balance[today] = self.current_capital
        
    def get_positions(self) -> List[PositionData]:
        """获取持仓列表"""
        return list(self._positions.values())
        
    def get_position(self, symbol: str, exchange: str) -> Optional[PositionData]:
        """获取指定持仓"""
        position_key = f"{symbol}.{exchange}"
        return self._positions.get(position_key)
        
    def get_total_value(self, prices: Dict[str, float] = None) -> float:
        """获取组合总价值"""
        total_value = self.current_capital
        
        if prices:
            # 计算持仓市值
            for position in self._positions.values():
                position_key = f"{position.symbol}.{position.exchange}"
                if position_key in prices:
                    current_price = prices[position_key]
                    if position.direction == Direction.LONG:
                        market_value = current_price * position.volume
                        cost_value = position.price * position.volume
                        unrealized_pnl = market_value - cost_value
                    else:
                        market_value = current_price * position.volume
                        cost_value = position.price * position.volume
                        unrealized_pnl = cost_value - market_value
                        
                    total_value += unrealized_pnl
                    
        return total_value
        
    def get_unrealized_pnl(self, prices: Dict[str, float] = None) -> float:
        """获取未实现盈亏"""
        if not prices:
            return 0.0
            
        unrealized_pnl = 0.0
        
        for position in self._positions.values():
            position_key = f"{position.symbol}.{position.exchange}"
            if position_key in prices:
                current_price = prices[position_key]
                if position.direction == Direction.LONG:
                    pnl = (current_price - position.price) * position.volume
                else:
                    pnl = (position.price - current_price) * position.volume
                    
                unrealized_pnl += pnl
                
        return unrealized_pnl
        
    def get_realized_pnl(self) -> float:
        """获取已实现盈亏"""
        return self._total_pnl
        
    def get_total_pnl(self, prices: Dict[str, float] = None) -> float:
        """获取总盈亏"""
        return self.get_realized_pnl() + self.get_unrealized_pnl(prices)
        
    def get_daily_pnl(self, start_date: date = None, end_date: date = None) -> Dict[date, float]:
        """获取每日盈亏"""
        if not start_date and not end_date:
            return self._daily_pnl.copy()
            
        filtered_pnl = {}
        for dt, pnl in self._daily_pnl.items():
            if start_date and dt < start_date:
                continue
            if end_date and dt > end_date:
                continue
            filtered_pnl[dt] = pnl
            
        return filtered_pnl
        
    def get_daily_balance(self, start_date: date = None, end_date: date = None) -> Dict[date, float]:
        """获取每日资金"""
        if not start_date and not end_date:
            return self._daily_balance.copy()
            
        filtered_balance = {}
        for dt, balance in self._daily_balance.items():
            if start_date and dt < start_date:
                continue
            if end_date and dt > end_date:
                continue
            filtered_balance[dt] = balance
            
        return filtered_balance
        
    def get_trades(self, symbol: str = None, start_date: date = None, end_date: date = None) -> List[TradeData]:
        """获取成交记录"""
        trades = self._trades.copy()
        
        # 按品种过滤
        if symbol:
            trades = [trade for trade in trades if trade.symbol == symbol]
            
        # 按日期过滤
        if start_date or end_date:
            filtered_trades = []
            for trade in trades:
                trade_date = trade.datetime.date()
                if start_date and trade_date < start_date:
                    continue
                if end_date and trade_date > end_date:
                    continue
                filtered_trades.append(trade)
            trades = filtered_trades
            
        return trades
        
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        # 计算胜率
        win_rate = self._win_trades / self._total_trades if self._total_trades > 0 else 0.0
        
        # 计算收益率
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        # 计算最大回撤
        max_drawdown = self._calculate_max_drawdown()
        
        statistics = {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'total_pnl': self._total_pnl,
            'total_commission': self._total_commission,
            'total_return': total_return,
            'total_trades': self._total_trades,
            'win_trades': self._win_trades,
            'lose_trades': self._lose_trades,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'position_count': len(self._positions),
        }
        
        return statistics
        
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if not self._daily_balance:
            return 0.0
            
        balances = list(self._daily_balance.values())
        max_balance = balances[0]
        max_drawdown = 0.0
        
        for balance in balances:
            if balance > max_balance:
                max_balance = balance
            else:
                drawdown = (max_balance - balance) / max_balance
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                    
        return max_drawdown
        
    def _print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        
        logger.info("=" * 50)
        logger.info("投资组合统计信息")
        logger.info("=" * 50)
        logger.info(f"初始资金: {stats['initial_capital']:,.2f}")
        logger.info(f"当前资金: {stats['current_capital']:,.2f}")
        logger.info(f"总盈亏: {stats['total_pnl']:,.2f}")
        logger.info(f"总手续费: {stats['total_commission']:,.2f}")
        logger.info(f"总收益率: {stats['total_return']:.2%}")
        logger.info(f"总交易次数: {stats['total_trades']}")
        logger.info(f"盈利次数: {stats['win_trades']}")
        logger.info(f"亏损次数: {stats['lose_trades']}")
        logger.info(f"胜率: {stats['win_rate']:.2%}")
        logger.info(f"最大回撤: {stats['max_drawdown']:.2%}")
        logger.info(f"持仓数量: {stats['position_count']}")
        logger.info("=" * 50)
        
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return True
