#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主引擎模块

系统的核心引擎，负责协调各个子模块的运行。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

import yaml
from loguru import logger

from .base import BaseObject, Event, EventType
from .event_engine import EventEngine


class MainEngine(BaseObject):
    """主引擎类"""
    
    def __init__(self, config_path: str = None):
        """初始化主引擎"""
        super().__init__()
        
        # 配置文件路径
        if config_path is None:
            config_path = Path(__file__).parent.parent.parent / "config"
        self.config_path = Path(config_path)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化日志
        self._init_logger()
        
        # 初始化事件引擎
        self.event_engine = EventEngine()
        
        # 子引擎字典
        self.engines: Dict[str, BaseObject] = {}
        
        # 运行状态
        self._active = False
        
        logger.info("主引擎初始化完成")
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        config = {}
        
        # 加载系统配置
        system_config_path = self.config_path / "system_config.yaml"
        if system_config_path.exists():
            with open(system_config_path, 'r', encoding='utf-8') as f:
                config['system'] = yaml.safe_load(f)
                
        # 加载交易配置
        trading_config_path = self.config_path / "trading_config.yaml"
        if trading_config_path.exists():
            with open(trading_config_path, 'r', encoding='utf-8') as f:
                config['trading'] = yaml.safe_load(f)
                
        # 加载数据配置
        data_config_path = self.config_path / "data_config.yaml"
        if data_config_path.exists():
            with open(data_config_path, 'r', encoding='utf-8') as f:
                config['data'] = yaml.safe_load(f)
                
        return config
        
    def _init_logger(self):
        """初始化日志系统"""
        # 移除默认处理器
        logger.remove()
        
        # 获取日志配置
        log_config = self.config.get('system', {}).get('logging', {})
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"),
            colorize=True
        )
        
        # 文件输出
        log_file = log_config.get('file_path', 'data/logs/system.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logger.add(
            log_file,
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"),
            rotation=log_config.get('rotation', '1 day'),
            retention=log_config.get('retention', '30 days'),
            compression=log_config.get('compression', 'zip'),
            encoding='utf-8'
        )
        
    def add_engine(self, name: str, engine: BaseObject):
        """添加子引擎"""
        self.engines[name] = engine
        logger.info(f"添加子引擎: {name}")
        
    def get_engine(self, name: str) -> Optional[BaseObject]:
        """获取子引擎"""
        return self.engines.get(name)
        
    def remove_engine(self, name: str):
        """移除子引擎"""
        if name in self.engines:
            engine = self.engines.pop(name)
            if hasattr(engine, 'stop'):
                engine.stop()
            logger.info(f"移除子引擎: {name}")
            
    def start(self):
        """启动主引擎"""
        if self._active:
            logger.warning("主引擎已经启动")
            return
            
        logger.info("启动主引擎...")
        
        # 启动事件引擎
        self.event_engine.start()
        
        # 发送系统启动事件
        event = Event(type=EventType.SYSTEM_START, source="MainEngine")
        self.event_engine.put(event)
        
        # 启动所有子引擎
        for name, engine in self.engines.items():
            try:
                if hasattr(engine, 'start'):
                    engine.start()
                    logger.info(f"启动子引擎: {name}")
            except Exception as e:
                logger.error(f"启动子引擎 {name} 失败: {e}")
                
        self._active = True
        logger.info("主引擎启动完成")
        
    def stop(self):
        """停止主引擎"""
        if not self._active:
            logger.warning("主引擎未启动")
            return
            
        logger.info("停止主引擎...")
        
        # 停止所有子引擎
        for name, engine in self.engines.items():
            try:
                if hasattr(engine, 'stop'):
                    engine.stop()
                    logger.info(f"停止子引擎: {name}")
            except Exception as e:
                logger.error(f"停止子引擎 {name} 失败: {e}")
                
        # 发送系统停止事件
        event = Event(type=EventType.SYSTEM_STOP, source="MainEngine")
        self.event_engine.put(event)
        
        # 停止事件引擎
        self.event_engine.stop()
        
        self._active = False
        logger.info("主引擎停止完成")
        
    def restart(self):
        """重启主引擎"""
        logger.info("重启主引擎...")
        self.stop()
        self.start()
        
    @property
    def active(self) -> bool:
        """获取运行状态"""
        return self._active
        
    def get_config(self, section: str = None) -> Dict:
        """获取配置"""
        if section is None:
            return self.config
        return self.config.get(section, {})
        
    def update_config(self, section: str, config: Dict):
        """更新配置"""
        self.config[section] = config
        logger.info(f"更新配置: {section}")
        
    def get_engine_list(self) -> List[str]:
        """获取子引擎列表"""
        return list(self.engines.keys())
        
    def get_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'active': self._active,
            'event_engine_active': self.event_engine.active,
            'event_queue_size': self.event_engine.queue_size,
            'engines': {}
        }
        
        for name, engine in self.engines.items():
            engine_status = {
                'name': name,
                'type': type(engine).__name__,
                'active': getattr(engine, 'active', None)
            }
            status['engines'][name] = engine_status
            
        return status
