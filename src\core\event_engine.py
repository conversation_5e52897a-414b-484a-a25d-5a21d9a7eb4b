#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
事件引擎模块

提供事件驱动的消息传递机制，支持事件的发布、订阅和处理。
"""

import threading
import time
from collections import defaultdict
from queue import Empty, Queue
from threading import Thread
from typing import Any, Callable, Dict, List

from .base import Event, EventType


class EventEngine:
    """事件引擎类"""
    
    def __init__(self):
        """初始化事件引擎"""
        self._queue: Queue = Queue()
        self._active = False
        self._thread: Thread = None
        self._timer: Thread = None
        self._handlers: Dict[EventType, List[Callable]] = defaultdict(list)
        self._general_handlers: List[Callable] = []
        
    def _run(self):
        """运行事件处理循环"""
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=1)
                self._process(event)
            except Empty:
                pass
            except Exception as e:
                print(f"事件处理异常: {e}")
                
    def _process(self, event: Event):
        """处理事件"""
        # 处理特定类型的事件处理器
        if event.type in self._handlers:
            for handler in self._handlers[event.type]:
                try:
                    handler(event)
                except Exception as e:
                    print(f"事件处理器异常: {e}")
                    
        # 处理通用事件处理器
        for handler in self._general_handlers:
            try:
                handler(event)
            except Exception as e:
                print(f"通用事件处理器异常: {e}")
                
    def _run_timer(self):
        """运行定时器"""
        while self._active:
            event = Event(type=EventType.TIMER)
            self.put(event)
            time.sleep(1)
            
    def start(self):
        """启动事件引擎"""
        if self._active:
            return
            
        self._active = True
        
        # 启动事件处理线程
        self._thread = Thread(target=self._run, name="EventEngine")
        self._thread.start()
        
        # 启动定时器线程
        self._timer = Thread(target=self._run_timer, name="EventTimer")
        self._timer.start()
        
        print("事件引擎启动成功")
        
    def stop(self):
        """停止事件引擎"""
        if not self._active:
            return
            
        self._active = False
        
        # 等待线程结束
        if self._thread and self._thread.is_alive():
            self._thread.join()
            
        if self._timer and self._timer.is_alive():
            self._timer.join()
            
        print("事件引擎停止成功")
        
    def put(self, event: Event):
        """发送事件"""
        self._queue.put(event)
        
    def register(self, type_: EventType, handler: Callable):
        """注册事件处理器"""
        handler_list = self._handlers[type_]
        if handler not in handler_list:
            handler_list.append(handler)
            
    def unregister(self, type_: EventType, handler: Callable):
        """注销事件处理器"""
        handler_list = self._handlers[type_]
        if handler in handler_list:
            handler_list.remove(handler)
            
        # 如果处理器列表为空，删除该类型
        if not handler_list:
            del self._handlers[type_]
            
    def register_general(self, handler: Callable):
        """注册通用事件处理器"""
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)
            
    def unregister_general(self, handler: Callable):
        """注销通用事件处理器"""
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)
            
    def get_handler_count(self, type_: EventType = None) -> int:
        """获取事件处理器数量"""
        if type_ is None:
            return sum(len(handlers) for handlers in self._handlers.values()) + len(self._general_handlers)
        else:
            return len(self._handlers.get(type_, []))
            
    def clear_handlers(self):
        """清空所有事件处理器"""
        self._handlers.clear()
        self._general_handlers.clear()
        
    @property
    def active(self) -> bool:
        """获取引擎状态"""
        return self._active
        
    @property
    def queue_size(self) -> int:
        """获取事件队列大小"""
        return self._queue.qsize()


class EventDispatcher:
    """事件分发器"""
    
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        
    def dispatch(self, type_: EventType, data: Any = None, source: str = ""):
        """分发事件"""
        event = Event(type=type_, data=data, source=source)
        self.event_engine.put(event)
        
    def dispatch_event(self, event: Event):
        """分发事件对象"""
        self.event_engine.put(event)
