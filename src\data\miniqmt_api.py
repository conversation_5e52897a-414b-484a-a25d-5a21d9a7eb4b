#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MiniQMT数据接口模块

集成xtdata接口，提供实时行情数据订阅和历史数据获取功能。
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
import threading

try:
    import xtdata
except ImportError:
    xtdata = None
    print("警告: xtdata模块未安装，MiniQMT功能将不可用")

import pandas as pd
import numpy as np
from loguru import logger

from ..core.base import BaseEngine, Event, EventType, TickData, BarData


class MiniqmtApi(BaseEngine):
    """MiniQMT数据接口类"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化MiniQMT接口"""
        super().__init__(main_engine, event_engine)
        
        # 连接状态
        self._connected = False
        self._connecting = False
        
        # 订阅管理
        self._subscriptions: Dict[str, Dict] = {}
        self._callbacks: Dict[str, Callable] = {}
        
        # 数据缓存
        self._tick_cache: Dict[str, TickData] = {}
        self._bar_cache: Dict[str, Dict[str, List[BarData]]] = {}
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('data', {}).get('sources', {}).get('miniqmt', {})
            
        # 连接参数
        self.host = self.config.get('host', '127.0.0.1')
        self.port = self.config.get('port', 58610)
        self.timeout = self.config.get('timeout', 30)
        self.retry_times = self.config.get('retry_times', 3)
        self.retry_interval = self.config.get('retry_interval', 5)
        
        logger.info("MiniQMT接口初始化完成")
        
    def start(self):
        """启动接口"""
        if not xtdata:
            logger.error("xtdata模块未安装，无法启动MiniQMT接口")
            return False
            
        logger.info("启动MiniQMT接口...")
        
        # 连接到MiniQMT
        success = self.connect()
        if success:
            logger.info("MiniQMT接口启动成功")
        else:
            logger.error("MiniQMT接口启动失败")
            
        return success
        
    def stop(self):
        """停止接口"""
        logger.info("停止MiniQMT接口...")
        
        # 取消所有订阅
        self.unsubscribe_all()
        
        # 断开连接
        self.disconnect()
        
        logger.info("MiniQMT接口已停止")
        
    def connect(self) -> bool:
        """连接到MiniQMT"""
        if not xtdata:
            return False
            
        if self._connected:
            logger.warning("已经连接到MiniQMT")
            return True
            
        if self._connecting:
            logger.warning("正在连接中...")
            return False
            
        self._connecting = True
        
        try:
            # 尝试连接
            for i in range(self.retry_times):
                try:
                    # 连接到指定端口
                    if hasattr(xtdata, 'reconnect'):
                        xtdata.reconnect(self.host, self.port)
                    
                    # 测试连接
                    test_data = xtdata.get_market_data(['000001.SZ'], period='1d', count=1)
                    if test_data:
                        self._connected = True
                        logger.info(f"成功连接到MiniQMT: {self.host}:{self.port}")
                        break
                        
                except Exception as e:
                    logger.warning(f"连接尝试 {i+1}/{self.retry_times} 失败: {e}")
                    if i < self.retry_times - 1:
                        time.sleep(self.retry_interval)
                        
            if not self._connected:
                logger.error("连接MiniQMT失败")
                
        except Exception as e:
            logger.error(f"连接MiniQMT异常: {e}")
            
        finally:
            self._connecting = False
            
        return self._connected
        
    def disconnect(self):
        """断开连接"""
        if self._connected:
            self._connected = False
            logger.info("已断开MiniQMT连接")
            
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected
        
    def subscribe_tick(self, symbol: str, callback: Callable = None) -> bool:
        """订阅Tick数据"""
        if not self._connected:
            logger.error("未连接到MiniQMT，无法订阅数据")
            return False
            
        try:
            # 设置回调函数
            def on_data(datas):
                for stock_code, data in datas.items():
                    tick_data = self._convert_tick_data(stock_code, data)
                    if tick_data:
                        self._tick_cache[stock_code] = tick_data
                        
                        # 发送事件
                        if self.event_engine:
                            event = Event(
                                type=EventType.TICK_DATA,
                                data=tick_data,
                                source="MiniqmtApi"
                            )
                            self.event_engine.put(event)
                            
                        # 调用用户回调
                        if callback:
                            callback(tick_data)
                            
            # 订阅数据
            seq = xtdata.subscribe_quote(symbol, period='tick', callback=on_data)
            
            if seq > 0:
                self._subscriptions[symbol] = {
                    'type': 'tick',
                    'seq': seq,
                    'callback': callback
                }
                logger.info(f"订阅Tick数据成功: {symbol}")
                return True
            else:
                logger.error(f"订阅Tick数据失败: {symbol}")
                return False
                
        except Exception as e:
            logger.error(f"订阅Tick数据异常: {e}")
            return False
            
    def subscribe_bar(self, symbol: str, period: str = '1m', callback: Callable = None) -> bool:
        """订阅K线数据"""
        if not self._connected:
            logger.error("未连接到MiniQMT，无法订阅数据")
            return False
            
        try:
            # 设置回调函数
            def on_data(datas):
                for stock_code, data_list in datas.items():
                    for data in data_list:
                        bar_data = self._convert_bar_data(stock_code, data, period)
                        if bar_data:
                            # 缓存数据
                            if stock_code not in self._bar_cache:
                                self._bar_cache[stock_code] = {}
                            if period not in self._bar_cache[stock_code]:
                                self._bar_cache[stock_code][period] = []
                                
                            self._bar_cache[stock_code][period].append(bar_data)
                            
                            # 发送事件
                            if self.event_engine:
                                event = Event(
                                    type=EventType.KLINE_DATA,
                                    data=bar_data,
                                    source="MiniqmtApi"
                                )
                                self.event_engine.put(event)
                                
                            # 调用用户回调
                            if callback:
                                callback(bar_data)
                                
            # 订阅数据
            seq = xtdata.subscribe_quote(symbol, period=period, callback=on_data)
            
            if seq > 0:
                sub_key = f"{symbol}_{period}"
                self._subscriptions[sub_key] = {
                    'type': 'bar',
                    'symbol': symbol,
                    'period': period,
                    'seq': seq,
                    'callback': callback
                }
                logger.info(f"订阅K线数据成功: {symbol} {period}")
                return True
            else:
                logger.error(f"订阅K线数据失败: {symbol} {period}")
                return False
                
        except Exception as e:
            logger.error(f"订阅K线数据异常: {e}")
            return False
            
    def unsubscribe(self, symbol: str, period: str = None):
        """取消订阅"""
        try:
            if period:
                sub_key = f"{symbol}_{period}"
            else:
                sub_key = symbol
                
            if sub_key in self._subscriptions:
                seq = self._subscriptions[sub_key]['seq']
                xtdata.unsubscribe_quote(seq)
                del self._subscriptions[sub_key]
                logger.info(f"取消订阅成功: {sub_key}")
            else:
                logger.warning(f"未找到订阅: {sub_key}")
                
        except Exception as e:
            logger.error(f"取消订阅异常: {e}")
            
    def unsubscribe_all(self):
        """取消所有订阅"""
        for sub_key in list(self._subscriptions.keys()):
            try:
                seq = self._subscriptions[sub_key]['seq']
                xtdata.unsubscribe_quote(seq)
                del self._subscriptions[sub_key]
            except Exception as e:
                logger.error(f"取消订阅 {sub_key} 异常: {e}")
                
        logger.info("已取消所有订阅")
        
    def get_history_data(self, symbol: str, period: str = '1d', 
                        start_time: str = '', end_time: str = '', 
                        count: int = -1) -> Optional[pd.DataFrame]:
        """获取历史数据"""
        if not self._connected:
            logger.error("未连接到MiniQMT，无法获取历史数据")
            return None
            
        try:
            # 获取数据
            data = xtdata.get_market_data(
                stock_list=[symbol],
                period=period,
                start_time=start_time,
                end_time=end_time,
                count=count
            )
            
            if data and symbol in data:
                # 转换为DataFrame
                df_data = {}
                for field, values in data.items():
                    if isinstance(values, dict) and symbol in values:
                        df_data[field] = values[symbol]
                        
                if df_data:
                    df = pd.DataFrame(df_data)
                    logger.info(f"获取历史数据成功: {symbol} {period} {len(df)}条")
                    return df
                    
            logger.warning(f"获取历史数据为空: {symbol} {period}")
            return None
            
        except Exception as e:
            logger.error(f"获取历史数据异常: {e}")
            return None
            
    def download_history_data(self, symbol: str, period: str = '1d',
                             start_time: str = '', end_time: str = '') -> bool:
        """下载历史数据"""
        if not self._connected:
            logger.error("未连接到MiniQMT，无法下载历史数据")
            return False
            
        try:
            xtdata.download_history_data(symbol, period, start_time, end_time)
            logger.info(f"下载历史数据成功: {symbol} {period}")
            return True
            
        except Exception as e:
            logger.error(f"下载历史数据异常: {e}")
            return False
            
    def get_instrument_info(self, symbol: str) -> Optional[Dict]:
        """获取合约信息"""
        if not self._connected:
            return None
            
        try:
            info = xtdata.get_instrument_detail(symbol)
            return info
            
        except Exception as e:
            logger.error(f"获取合约信息异常: {e}")
            return None
            
    def _convert_tick_data(self, symbol: str, data: Any) -> Optional[TickData]:
        """转换Tick数据"""
        try:
            # 解析交易所和代码
            if '.' in symbol:
                code, exchange = symbol.split('.')
            else:
                code = symbol
                exchange = ""
                
            # 创建TickData对象
            tick = TickData(
                symbol=symbol,
                exchange=exchange,
                datetime=datetime.fromtimestamp(data.get('time', 0) / 1000),
                last_price=data.get('lastPrice', 0.0),
                volume=data.get('volume', 0.0),
                turnover=data.get('amount', 0.0),
                open_price=data.get('open', 0.0),
                high_price=data.get('high', 0.0),
                low_price=data.get('low', 0.0),
                pre_close=data.get('lastClose', 0.0),
                bid_price_1=data.get('bidPrice', [0.0])[0] if data.get('bidPrice') else 0.0,
                ask_price_1=data.get('askPrice', [0.0])[0] if data.get('askPrice') else 0.0,
                bid_volume_1=data.get('bidVol', [0.0])[0] if data.get('bidVol') else 0.0,
                ask_volume_1=data.get('askVol', [0.0])[0] if data.get('askVol') else 0.0,
            )
            
            return tick
            
        except Exception as e:
            logger.error(f"转换Tick数据异常: {e}")
            return None
            
    def _convert_bar_data(self, symbol: str, data: Any, period: str) -> Optional[BarData]:
        """转换K线数据"""
        try:
            # 解析交易所和代码
            if '.' in symbol:
                code, exchange = symbol.split('.')
            else:
                code = symbol
                exchange = ""
                
            # 创建BarData对象
            bar = BarData(
                symbol=symbol,
                exchange=exchange,
                datetime=datetime.fromtimestamp(data.get('time', 0) / 1000),
                interval=period,
                open_price=data.get('open', 0.0),
                high_price=data.get('high', 0.0),
                low_price=data.get('low', 0.0),
                close_price=data.get('close', 0.0),
                volume=data.get('volume', 0.0),
                turnover=data.get('amount', 0.0),
                open_interest=data.get('openInterest', 0.0),
            )
            
            return bar
            
        except Exception as e:
            logger.error(f"转换K线数据异常: {e}")
            return None
            
    def get_subscriptions(self) -> Dict[str, Dict]:
        """获取订阅列表"""
        return self._subscriptions.copy()
        
    def get_tick_data(self, symbol: str) -> Optional[TickData]:
        """获取最新Tick数据"""
        return self._tick_cache.get(symbol)
        
    def get_bar_data(self, symbol: str, period: str) -> List[BarData]:
        """获取K线数据"""
        if symbol in self._bar_cache and period in self._bar_cache[symbol]:
            return self._bar_cache[symbol][period].copy()
        return []

    def run_event_loop(self):
        """运行事件循环（用于维持订阅）"""
        if not xtdata:
            return

        try:
            xtdata.run()
        except Exception as e:
            logger.error(f"事件循环异常: {e}")

    @property
    def active(self) -> bool:
        """获取活动状态"""
        return self._connected
