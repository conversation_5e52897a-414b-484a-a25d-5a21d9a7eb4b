#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
移动平均线交叉策略

当短期均线上穿长期均线时买入，下穿时卖出。
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.strategy.base import BaseStrategy
from src.strategy.indicators import SMA, EMA
from src.core.base import TickData, BarData, OrderData, TradeData, OrderType


class MovingAverageCrossStrategy(BaseStrategy):
    """移动平均线交叉策略"""
    
    def __init__(self, strategy_name: str, symbols: list):
        super().__init__(strategy_name, symbols)
        
        # 添加策略参数
        self.add_parameter("short_period", 5, "短期均线周期", int, 3, 20)
        self.add_parameter("long_period", 20, "长期均线周期", int, 10, 100)
        self.add_parameter("ma_type", "SMA", "均线类型", str)
        self.add_parameter("position_size", 1000, "每次交易数量", int, 100, 10000)
        self.add_parameter("stop_loss_pct", 0.05, "止损百分比", float, 0.01, 0.2)
        self.add_parameter("take_profit_pct", 0.1, "止盈百分比", float, 0.02, 0.5)
        
        # 技术指标
        self.short_ma = {}
        self.long_ma = {}
        
        # 交易状态
        self.positions = {}  # 持仓状态
        self.entry_prices = {}  # 入场价格
        self.last_signals = {}  # 上次信号
        
        # 初始化指标
        self._init_indicators()
        
    def _init_indicators(self):
        """初始化技术指标"""
        short_period = self.get_parameter("short_period")
        long_period = self.get_parameter("long_period")
        ma_type = self.get_parameter("ma_type")
        
        for symbol in self.symbols:
            if ma_type == "EMA":
                self.short_ma[symbol] = EMA(short_period)
                self.long_ma[symbol] = EMA(long_period)
            else:
                self.short_ma[symbol] = SMA(short_period)
                self.long_ma[symbol] = SMA(long_period)
                
            self.positions[symbol] = 0  # 0: 空仓, 1: 多头, -1: 空头
            self.entry_prices[symbol] = 0.0
            self.last_signals[symbol] = 0  # 0: 无信号, 1: 买入, -1: 卖出
            
    def on_start(self):
        """策略启动"""
        self.write_log("移动平均线交叉策略启动")
        self.write_log(f"短期均线周期: {self.get_parameter('short_period')}")
        self.write_log(f"长期均线周期: {self.get_parameter('long_period')}")
        self.write_log(f"均线类型: {self.get_parameter('ma_type')}")
        
    def on_stop(self):
        """策略停止"""
        self.write_log("移动平均线交叉策略停止")
        
        # 平仓所有持仓
        for symbol in self.symbols:
            if self.positions[symbol] != 0:
                self._close_position(symbol)
                
    def on_tick_data(self, tick: TickData):
        """处理Tick数据"""
        # 这个策略基于K线数据，不处理Tick
        pass
        
    def on_bar_data(self, bar: BarData):
        """处理K线数据"""
        symbol = bar.symbol
        
        if symbol not in self.symbols:
            return
            
        # 更新技术指标
        short_ma_value = self.short_ma[symbol].update(bar.close_price)
        long_ma_value = self.long_ma[symbol].update(bar.close_price)
        
        if short_ma_value is None or long_ma_value is None:
            return
            
        # 生成交易信号
        signal = self._generate_signal(symbol, short_ma_value, long_ma_value, bar.close_price)
        
        if signal != 0:
            self._execute_signal(symbol, signal, bar.close_price, bar.datetime)
            
    def _generate_signal(self, symbol: str, short_ma: float, long_ma: float, price: float) -> int:
        """生成交易信号"""
        current_signal = 0
        
        # 均线交叉信号
        if short_ma > long_ma:
            current_signal = 1  # 买入信号
        elif short_ma < long_ma:
            current_signal = -1  # 卖出信号
            
        # 检查信号变化
        last_signal = self.last_signals[symbol]
        
        if current_signal != last_signal:
            self.last_signals[symbol] = current_signal
            
            # 金叉买入
            if current_signal == 1 and last_signal <= 0:
                return 1
            # 死叉卖出
            elif current_signal == -1 and last_signal >= 0:
                return -1
                
        # 检查止损止盈
        if self.positions[symbol] != 0:
            entry_price = self.entry_prices[symbol]
            stop_loss_pct = self.get_parameter("stop_loss_pct")
            take_profit_pct = self.get_parameter("take_profit_pct")
            
            if self.positions[symbol] == 1:  # 多头持仓
                # 止损
                if price <= entry_price * (1 - stop_loss_pct):
                    self.write_log(f"{symbol} 触发止损: {price:.2f} <= {entry_price * (1 - stop_loss_pct):.2f}")
                    return -1
                # 止盈
                elif price >= entry_price * (1 + take_profit_pct):
                    self.write_log(f"{symbol} 触发止盈: {price:.2f} >= {entry_price * (1 + take_profit_pct):.2f}")
                    return -1
                    
            elif self.positions[symbol] == -1:  # 空头持仓
                # 止损
                if price >= entry_price * (1 + stop_loss_pct):
                    self.write_log(f"{symbol} 触发止损: {price:.2f} >= {entry_price * (1 + stop_loss_pct):.2f}")
                    return 1
                # 止盈
                elif price <= entry_price * (1 - take_profit_pct):
                    self.write_log(f"{symbol} 触发止盈: {price:.2f} <= {entry_price * (1 - take_profit_pct):.2f}")
                    return 1
                    
        return 0
        
    def _execute_signal(self, symbol: str, signal: int, price: float, datetime):
        """执行交易信号"""
        position_size = self.get_parameter("position_size")
        
        if signal == 1:  # 买入信号
            if self.positions[symbol] <= 0:
                # 如果有空头持仓，先平仓
                if self.positions[symbol] == -1:
                    self._close_position(symbol, price)
                    
                # 开多头
                order_id = self.buy(symbol, price, position_size, OrderType.MARKET)
                if order_id:
                    self.positions[symbol] = 1
                    self.entry_prices[symbol] = price
                    self.write_log(f"{symbol} 买入开仓: {position_size}@{price:.2f}")
                    
        elif signal == -1:  # 卖出信号
            if self.positions[symbol] >= 0:
                # 如果有多头持仓，先平仓
                if self.positions[symbol] == 1:
                    self._close_position(symbol, price)
                    
                # 开空头（如果支持做空）
                # 这里简化处理，只做多不做空
                # order_id = self.short(symbol, price, position_size, OrderType.MARKET)
                # if order_id:
                #     self.positions[symbol] = -1
                #     self.entry_prices[symbol] = price
                #     self.write_log(f"{symbol} 卖出开仓: {position_size}@{price:.2f}")
                    
    def _close_position(self, symbol: str, price: float = None):
        """平仓"""
        if self.positions[symbol] == 0:
            return
            
        position_size = self.get_parameter("position_size")
        
        if self.positions[symbol] == 1:  # 平多头
            if price:
                order_id = self.sell(symbol, price, position_size, OrderType.MARKET)
            else:
                # 获取当前价格
                bars = self.get_bars(symbol, 1)
                if bars:
                    current_price = bars[-1].close_price
                    order_id = self.sell(symbol, current_price, position_size, OrderType.MARKET)
                else:
                    return
                    
            if order_id:
                entry_price = self.entry_prices[symbol]
                pnl = (price - entry_price) * position_size if price else 0
                self.write_log(f"{symbol} 卖出平仓: {position_size}@{price:.2f if price else 0:.2f}, 盈亏: {pnl:.2f}")
                self.positions[symbol] = 0
                self.entry_prices[symbol] = 0.0
                
        elif self.positions[symbol] == -1:  # 平空头
            if price:
                order_id = self.cover(symbol, price, position_size, OrderType.MARKET)
            else:
                # 获取当前价格
                bars = self.get_bars(symbol, 1)
                if bars:
                    current_price = bars[-1].close_price
                    order_id = self.cover(symbol, current_price, position_size, OrderType.MARKET)
                else:
                    return
                    
            if order_id:
                entry_price = self.entry_prices[symbol]
                pnl = (entry_price - price) * position_size if price else 0
                self.write_log(f"{symbol} 买入平仓: {position_size}@{price:.2f if price else 0:.2f}, 盈亏: {pnl:.2f}")
                self.positions[symbol] = 0
                self.entry_prices[symbol] = 0.0
                
    def on_order_update(self, order: OrderData):
        """处理订单更新"""
        self.write_log(f"订单更新: {order.orderid} {order.symbol} {order.status.value}")
        
    def on_trade_update(self, trade: TradeData):
        """处理成交更新"""
        self.write_log(f"成交更新: {trade.tradeid} {trade.symbol} {trade.direction.value} "
                      f"{trade.volume}@{trade.price}")
                      
        # 更新统计
        self.total_trades += 1
        
    def get_current_positions(self) -> dict:
        """获取当前持仓"""
        return {symbol: {
            'position': self.positions[symbol],
            'entry_price': self.entry_prices[symbol]
        } for symbol in self.symbols}
        
    def get_indicator_values(self, symbol: str) -> dict:
        """获取指标值"""
        if symbol not in self.symbols:
            return {}
            
        return {
            'short_ma': self.short_ma[symbol].get_value() if self.short_ma[symbol].get_value() else 0,
            'long_ma': self.long_ma[symbol].get_value() if self.long_ma[symbol].get_value() else 0,
            'last_signal': self.last_signals[symbol]
        }
