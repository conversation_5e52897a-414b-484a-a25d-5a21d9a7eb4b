#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险管理模块

提供风险控制、资金管理、止损止盈等功能。
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
from enum import Enum

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, OrderData, TradeData, PositionData, AccountData,
    Direction, Offset, OrderStatus
)


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskManager(BaseEngine):
    """风险管理器"""
    
    def __init__(self, main_engine=None, event_engine=None):
        """初始化风险管理器"""
        super().__init__(main_engine, event_engine)
        
        # 风险状态
        self._risk_level = RiskLevel.LOW
        self._risk_alerts: List[Dict] = []
        
        # 交易统计
        self._daily_trades: Dict[date, int] = defaultdict(int)
        self._daily_pnl: Dict[date, float] = defaultdict(float)
        self._position_sizes: Dict[str, float] = defaultdict(float)
        
        # 配置参数
        self.config = {}
        if main_engine:
            self.config = main_engine.get_config('trading', {})
            
        # 风险控制配置
        self.risk_config = self.config.get('risk_management', {})
        
        # 全局风险控制
        self.global_config = self.risk_config.get('global', {})
        self.max_position_ratio = self.global_config.get('max_position_ratio', 0.95)
        self.max_single_position = self.global_config.get('max_single_position', 0.20)
        self.max_daily_loss = self.global_config.get('max_daily_loss', 0.05)
        self.max_drawdown = self.global_config.get('max_drawdown', 0.15)
        
        # 策略级风险控制
        self.strategy_config = self.risk_config.get('strategy', {})
        self.max_strategy_position = self.strategy_config.get('max_position_ratio', 0.30)
        self.max_daily_trades = self.strategy_config.get('max_daily_trades', 100)
        self.min_interval_seconds = self.strategy_config.get('min_interval_seconds', 1)
        
        # 品种级风险控制
        self.instrument_config = self.risk_config.get('instrument', {})
        self.max_instrument_position = self.instrument_config.get('max_position_ratio', 0.10)
        self.max_order_size = self.instrument_config.get('max_order_size', 1000)
        
        # 资金管理配置
        self.capital_config = self.config.get('capital_management', {})
        self.initial_capital = self.capital_config.get('initial_capital', 1000000.0)
        
        # 最后交易时间记录
        self._last_trade_time: Dict[str, datetime] = {}
        
        # 注册事件处理器
        if event_engine:
            event_engine.register(EventType.ORDER_SUBMIT, self._on_order_submit)
            event_engine.register(EventType.ORDER_FILLED, self._on_trade)
            event_engine.register(EventType.RISK_CHECK, self._on_risk_check)
            
        logger.info("风险管理器初始化完成")
        
    def start(self):
        """启动风险管理器"""
        logger.info("启动风险管理器...")
        
        # 初始化风险状态
        self._risk_level = RiskLevel.LOW
        self._risk_alerts.clear()
        
        logger.info("风险管理器启动完成")
        
    def stop(self):
        """停止风险管理器"""
        logger.info("停止风险管理器...")
        
        # 输出风险报告
        self._print_risk_report()
        
        logger.info("风险管理器已停止")
        
    def check_order_risk(self, order: OrderData, account: AccountData = None,
                        positions: List[PositionData] = None) -> Tuple[bool, str]:
        """检查订单风险"""
        try:
            # 1. 检查订单基本信息
            if not self._check_order_basic(order):
                return False, "订单基本信息检查失败"
                
            # 2. 检查交易时间间隔
            if not self._check_trade_interval(order):
                return False, "交易时间间隔过短"
                
            # 3. 检查单笔订单限额
            if not self._check_order_size(order):
                return False, "单笔订单超过限额"
                
            # 4. 检查每日交易次数
            if not self._check_daily_trades(order):
                return False, "每日交易次数超限"
                
            # 5. 检查持仓限额
            if not self._check_position_limit(order, positions):
                return False, "持仓限额检查失败"
                
            # 6. 检查资金充足性
            if account and not self._check_funds(order, account):
                return False, "资金不足"
                
            # 7. 检查每日亏损限额
            if account and not self._check_daily_loss(account):
                return False, "每日亏损超限"
                
            return True, "风险检查通过"
            
        except Exception as e:
            logger.error(f"订单风险检查异常: {e}")
            return False, f"风险检查异常: {e}"
            
    def _check_order_basic(self, order: OrderData) -> bool:
        """检查订单基本信息"""
        # 检查必要字段
        if not order.symbol or not order.exchange:
            return False
            
        # 检查价格和数量
        if order.price <= 0 or order.volume <= 0:
            return False
            
        return True
        
    def _check_trade_interval(self, order: OrderData) -> bool:
        """检查交易时间间隔"""
        key = f"{order.symbol}.{order.exchange}"
        
        if key in self._last_trade_time:
            last_time = self._last_trade_time[key]
            interval = (datetime.now() - last_time).total_seconds()
            
            if interval < self.min_interval_seconds:
                return False
                
        return True
        
    def _check_order_size(self, order: OrderData) -> bool:
        """检查单笔订单限额"""
        if order.volume > self.max_order_size:
            return False
            
        return True
        
    def _check_daily_trades(self, order: OrderData) -> bool:
        """检查每日交易次数"""
        today = date.today()
        daily_count = self._daily_trades[today]
        
        if daily_count >= self.max_daily_trades:
            return False
            
        return True
        
    def _check_position_limit(self, order: OrderData, positions: List[PositionData] = None) -> bool:
        """检查持仓限额"""
        if not positions:
            return True
            
        # 计算当前持仓比例
        total_position_value = 0.0
        instrument_position_value = 0.0
        
        for position in positions:
            position_value = position.volume * position.price
            total_position_value += position_value
            
            if position.symbol == order.symbol and position.exchange == order.exchange:
                instrument_position_value += position_value
                
        # 检查总持仓比例
        total_ratio = total_position_value / self.initial_capital
        if total_ratio > self.max_position_ratio:
            return False
            
        # 检查单品种持仓比例
        order_value = order.volume * order.price
        new_instrument_value = instrument_position_value + order_value
        instrument_ratio = new_instrument_value / self.initial_capital
        
        if instrument_ratio > self.max_instrument_position:
            return False
            
        return True
        
    def _check_funds(self, order: OrderData, account: AccountData) -> bool:
        """检查资金充足性"""
        # 计算所需资金
        required_funds = self._calculate_required_funds(order)
        
        # 检查可用资金
        if account.available < required_funds:
            return False
            
        return True
        
    def _calculate_required_funds(self, order: OrderData) -> float:
        """计算所需资金"""
        # 简化计算，实际应根据品种类型、保证金比例等计算
        if order.direction == Direction.LONG:
            return order.price * order.volume
        else:
            # 做空可能需要保证金
            return order.price * order.volume * 0.1  # 假设10%保证金
            
    def _check_daily_loss(self, account: AccountData) -> bool:
        """检查每日亏损限额"""
        today = date.today()
        daily_pnl = self._daily_pnl[today]
        
        # 计算亏损比例
        if daily_pnl < 0:
            loss_ratio = abs(daily_pnl) / self.initial_capital
            if loss_ratio > self.max_daily_loss:
                return False
                
        return True
        
    def update_risk_level(self, account: AccountData = None, positions: List[PositionData] = None):
        """更新风险等级"""
        try:
            risk_score = 0
            
            # 1. 资金风险评估
            if account:
                # 计算资金使用率
                used_ratio = (account.balance - account.available) / account.balance
                if used_ratio > 0.8:
                    risk_score += 3
                elif used_ratio > 0.6:
                    risk_score += 2
                elif used_ratio > 0.4:
                    risk_score += 1
                    
                # 计算每日亏损
                today = date.today()
                daily_pnl = self._daily_pnl[today]
                if daily_pnl < 0:
                    loss_ratio = abs(daily_pnl) / self.initial_capital
                    if loss_ratio > 0.03:
                        risk_score += 3
                    elif loss_ratio > 0.02:
                        risk_score += 2
                    elif loss_ratio > 0.01:
                        risk_score += 1
                        
            # 2. 持仓风险评估
            if positions:
                total_position_value = sum(pos.volume * pos.price for pos in positions)
                position_ratio = total_position_value / self.initial_capital
                
                if position_ratio > 0.8:
                    risk_score += 3
                elif position_ratio > 0.6:
                    risk_score += 2
                elif position_ratio > 0.4:
                    risk_score += 1
                    
            # 3. 交易频率风险评估
            today = date.today()
            daily_trades = self._daily_trades[today]
            if daily_trades > 50:
                risk_score += 2
            elif daily_trades > 20:
                risk_score += 1
                
            # 确定风险等级
            if risk_score >= 6:
                self._risk_level = RiskLevel.CRITICAL
            elif risk_score >= 4:
                self._risk_level = RiskLevel.HIGH
            elif risk_score >= 2:
                self._risk_level = RiskLevel.MEDIUM
            else:
                self._risk_level = RiskLevel.LOW
                
            # 发送风险警报
            if self._risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                self._send_risk_alert(self._risk_level, risk_score)
                
        except Exception as e:
            logger.error(f"更新风险等级异常: {e}")
            
    def _send_risk_alert(self, risk_level: RiskLevel, risk_score: int):
        """发送风险警报"""
        alert = {
            'timestamp': datetime.now(),
            'level': risk_level,
            'score': risk_score,
            'message': f"风险等级: {risk_level.value}, 风险评分: {risk_score}"
        }
        
        self._risk_alerts.append(alert)
        
        # 发送风险警报事件
        event = Event(
            type=EventType.RISK_ALERT,
            data=alert,
            source="RiskManager"
        )
        if self.event_engine:
            self.event_engine.put(event)
            
        logger.warning(f"风险警报: {alert['message']}")
        
    def _on_order_submit(self, event: Event):
        """处理订单提交事件"""
        order: OrderData = event.data
        if not order:
            return
            
        # 更新交易时间记录
        key = f"{order.symbol}.{order.exchange}"
        self._last_trade_time[key] = datetime.now()
        
        # 更新每日交易次数
        today = date.today()
        self._daily_trades[today] += 1
        
    def _on_trade(self, event: Event):
        """处理成交事件"""
        trade: TradeData = event.data
        if not trade:
            return
            
        # 更新持仓统计
        key = f"{trade.symbol}.{trade.exchange}"
        if trade.offset == Offset.OPEN:
            self._position_sizes[key] += trade.volume
        else:
            self._position_sizes[key] -= trade.volume
            
        # 更新每日盈亏（简化计算）
        today = date.today()
        # 这里需要根据实际成交价格和持仓成本计算盈亏
        # 暂时简化处理
        
    def _on_risk_check(self, event: Event):
        """处理风险检查事件"""
        # 定期风险检查
        self.update_risk_level()
        
    def get_risk_level(self) -> RiskLevel:
        """获取当前风险等级"""
        return self._risk_level
        
    def get_risk_alerts(self, limit: int = None) -> List[Dict]:
        """获取风险警报"""
        alerts = self._risk_alerts.copy()
        if limit:
            alerts = alerts[-limit:]
        return alerts
        
    def get_daily_trades_count(self, target_date: date = None) -> int:
        """获取每日交易次数"""
        if target_date is None:
            target_date = date.today()
        return self._daily_trades[target_date]
        
    def get_position_sizes(self) -> Dict[str, float]:
        """获取持仓规模"""
        return self._position_sizes.copy()
        
    def clear_daily_stats(self, target_date: date = None):
        """清空每日统计"""
        if target_date is None:
            target_date = date.today()
            
        if target_date in self._daily_trades:
            del self._daily_trades[target_date]
            
        if target_date in self._daily_pnl:
            del self._daily_pnl[target_date]
            
        logger.info(f"清空 {target_date} 的每日统计数据")
        
    def _print_risk_report(self):
        """打印风险报告"""
        logger.info("=" * 50)
        logger.info("风险管理报告")
        logger.info("=" * 50)
        logger.info(f"当前风险等级: {self._risk_level.value}")
        logger.info(f"风险警报数量: {len(self._risk_alerts)}")
        logger.info(f"今日交易次数: {self.get_daily_trades_count()}")
        logger.info(f"持仓品种数量: {len(self._position_sizes)}")
        
        # 显示最近的风险警报
        recent_alerts = self.get_risk_alerts(5)
        if recent_alerts:
            logger.info("最近风险警报:")
            for alert in recent_alerts:
                logger.info(f"  {alert['timestamp']}: {alert['message']}")
                
        logger.info("=" * 50)
        
    @property
    def active(self) -> bool:
        """获取活动状态"""
        return True
