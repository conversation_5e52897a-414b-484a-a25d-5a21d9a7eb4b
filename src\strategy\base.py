#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略基础模块

提供策略开发的基础类和接口。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict

from loguru import logger

from ..core.base import (
    BaseEngine, Event, EventType, TickData, BarData, OrderData, TradeData,
    PositionData, AccountData, Direction, Offset, OrderType
)


class StrategyParameter:
    """策略参数类"""
    
    def __init__(self, name: str, value: Any, description: str = "", 
                 param_type: type = str, min_value: Any = None, max_value: Any = None):
        self.name = name
        self.value = value
        self.description = description
        self.param_type = param_type
        self.min_value = min_value
        self.max_value = max_value
        
    def validate(self, value: Any) -> bool:
        """验证参数值"""
        try:
            # 类型检查
            if not isinstance(value, self.param_type):
                return False
                
            # 范围检查
            if self.min_value is not None and value < self.min_value:
                return False
            if self.max_value is not None and value > self.max_value:
                return False
                
            return True
        except:
            return False
            
    def set_value(self, value: Any) -> bool:
        """设置参数值"""
        if self.validate(value):
            self.value = value
            return True
        return False


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, strategy_name: str, symbols: List[str]):
        """初始化策略"""
        self.strategy_name = strategy_name
        self.symbols = symbols
        
        # 策略状态
        self.active = False
        self.trading = False
        
        # 策略参数
        self.parameters: Dict[str, StrategyParameter] = {}
        
        # 数据存储
        self.bars: Dict[str, List[BarData]] = defaultdict(list)
        self.ticks: Dict[str, List[TickData]] = defaultdict(list)
        
        # 交易相关
        self.positions: Dict[str, PositionData] = {}
        self.orders: Dict[str, OrderData] = {}
        self.trades: List[TradeData] = []
        
        # 引擎引用
        self.main_engine: Optional[Any] = None
        self.trader: Optional[Any] = None
        self.portfolio: Optional[Any] = None
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # 统计信息
        self.total_trades = 0
        self.win_trades = 0
        self.lose_trades = 0
        self.total_pnl = 0.0
        
        logger.info(f"策略 {strategy_name} 初始化完成，品种: {symbols}")
        
    def add_parameter(self, name: str, value: Any, description: str = "",
                     param_type: type = str, min_value: Any = None, max_value: Any = None):
        """添加策略参数"""
        param = StrategyParameter(name, value, description, param_type, min_value, max_value)
        self.parameters[name] = param
        
    def get_parameter(self, name: str) -> Any:
        """获取参数值"""
        if name in self.parameters:
            return self.parameters[name].value
        return None
        
    def set_parameter(self, name: str, value: Any) -> bool:
        """设置参数值"""
        if name in self.parameters:
            return self.parameters[name].set_value(value)
        return False
        
    def get_parameters_info(self) -> Dict:
        """获取参数信息"""
        info = {}
        for name, param in self.parameters.items():
            info[name] = {
                'value': param.value,
                'description': param.description,
                'type': param.param_type.__name__,
                'min_value': param.min_value,
                'max_value': param.max_value
            }
        return info
        
    def set_engines(self, main_engine, trader=None, portfolio=None):
        """设置引擎引用"""
        self.main_engine = main_engine
        self.trader = trader
        self.portfolio = portfolio
        
    def start(self):
        """启动策略"""
        self.active = True
        self.trading = True
        self.on_start()
        logger.info(f"策略 {self.strategy_name} 已启动")
        
    def stop(self):
        """停止策略"""
        self.active = False
        self.trading = False
        self.on_stop()
        logger.info(f"策略 {self.strategy_name} 已停止")
        
    def pause_trading(self):
        """暂停交易"""
        self.trading = False
        logger.info(f"策略 {self.strategy_name} 交易已暂停")
        
    def resume_trading(self):
        """恢复交易"""
        self.trading = True
        logger.info(f"策略 {self.strategy_name} 交易已恢复")
        
    def on_tick(self, tick: TickData):
        """处理Tick数据"""
        if not self.active:
            return
            
        # 保存Tick数据
        symbol = tick.symbol
        self.ticks[symbol].append(tick)
        
        # 限制数据长度
        if len(self.ticks[symbol]) > 1000:
            self.ticks[symbol].pop(0)
            
        # 调用策略实现
        try:
            self.on_tick_data(tick)
        except Exception as e:
            logger.error(f"策略 {self.strategy_name} 处理Tick数据异常: {e}")
            
    def on_bar(self, bar: BarData):
        """处理K线数据"""
        if not self.active:
            return
            
        # 保存K线数据
        symbol = bar.symbol
        self.bars[symbol].append(bar)
        
        # 限制数据长度
        if len(self.bars[symbol]) > 1000:
            self.bars[symbol].pop(0)
            
        # 调用策略实现
        try:
            self.on_bar_data(bar)
        except Exception as e:
            logger.error(f"策略 {self.strategy_name} 处理K线数据异常: {e}")
            
    def on_order(self, order: OrderData):
        """处理订单更新"""
        if not self.active:
            return
            
        # 保存订单
        self.orders[order.orderid] = order
        
        # 调用策略实现
        try:
            self.on_order_update(order)
        except Exception as e:
            logger.error(f"策略 {self.strategy_name} 处理订单更新异常: {e}")
            
    def on_trade(self, trade: TradeData):
        """处理成交更新"""
        if not self.active:
            return
            
        # 保存成交
        self.trades.append(trade)
        self.total_trades += 1
        
        # 更新统计
        # 这里简化处理，实际应该根据开平仓计算盈亏
        
        # 调用策略实现
        try:
            self.on_trade_update(trade)
        except Exception as e:
            logger.error(f"策略 {self.strategy_name} 处理成交更新异常: {e}")
            
    def buy(self, symbol: str, price: float, volume: float, 
           order_type: OrderType = OrderType.LIMIT) -> Optional[str]:
        """买入"""
        if not self.trading or not self.trader:
            return None
            
        return self.trader.submit_order(
            symbol=symbol,
            exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=price,
            volume=volume,
            order_type=order_type
        )
        
    def sell(self, symbol: str, price: float, volume: float,
            order_type: OrderType = OrderType.LIMIT) -> Optional[str]:
        """卖出"""
        if not self.trading or not self.trader:
            return None
            
        return self.trader.submit_order(
            symbol=symbol,
            exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
            direction=Direction.SHORT,
            offset=Offset.CLOSE,
            price=price,
            volume=volume,
            order_type=order_type
        )
        
    def short(self, symbol: str, price: float, volume: float,
             order_type: OrderType = OrderType.LIMIT) -> Optional[str]:
        """做空"""
        if not self.trading or not self.trader:
            return None
            
        return self.trader.submit_order(
            symbol=symbol,
            exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
            direction=Direction.SHORT,
            offset=Offset.OPEN,
            price=price,
            volume=volume,
            order_type=order_type
        )
        
    def cover(self, symbol: str, price: float, volume: float,
             order_type: OrderType = OrderType.LIMIT) -> Optional[str]:
        """平空"""
        if not self.trading or not self.trader:
            return None
            
        return self.trader.submit_order(
            symbol=symbol,
            exchange=symbol.split('.')[1] if '.' in symbol else 'UNKNOWN',
            direction=Direction.LONG,
            offset=Offset.CLOSE,
            price=price,
            volume=volume,
            order_type=order_type
        )
        
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        if not self.trader:
            return False
            
        return self.trader.cancel_order(order_id)
        
    def cancel_all_orders(self) -> int:
        """撤销所有订单"""
        if not self.trader:
            return 0
            
        return self.trader.cancel_all_orders()
        
    def get_position(self, symbol: str) -> Optional[PositionData]:
        """获取持仓"""
        if not self.trader:
            return None
            
        # 简化处理，实际应该区分多空方向
        positions = self.trader.get_positions(symbol)
        return positions[0] if positions else None
        
    def get_account(self) -> Optional[AccountData]:
        """获取账户"""
        if not self.trader:
            return None
            
        return self.trader.get_account()
        
    def get_bars(self, symbol: str, count: int = None) -> List[BarData]:
        """获取K线数据"""
        bars = self.bars.get(symbol, [])
        if count is None:
            return bars.copy()
        else:
            return bars[-count:] if len(bars) >= count else bars.copy()
            
    def get_ticks(self, symbol: str, count: int = None) -> List[TickData]:
        """获取Tick数据"""
        ticks = self.ticks.get(symbol, [])
        if count is None:
            return ticks.copy()
        else:
            return ticks[-count:] if len(ticks) >= count else ticks.copy()
            
    def write_log(self, message: str, level: str = "INFO"):
        """写日志"""
        log_message = f"[{self.strategy_name}] {message}"
        
        if level.upper() == "DEBUG":
            logger.debug(log_message)
        elif level.upper() == "INFO":
            logger.info(log_message)
        elif level.upper() == "WARNING":
            logger.warning(log_message)
        elif level.upper() == "ERROR":
            logger.error(log_message)
        else:
            logger.info(log_message)
            
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        self.callbacks[event_type].append(callback)
        
    def remove_callback(self, event_type: str, callback: Callable):
        """移除回调函数"""
        if event_type in self.callbacks and callback in self.callbacks[event_type]:
            self.callbacks[event_type].remove(callback)
            
    def trigger_callback(self, event_type: str, *args, **kwargs):
        """触发回调函数"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"策略 {self.strategy_name} 回调函数异常: {e}")
                
    def get_statistics(self) -> Dict:
        """获取策略统计信息"""
        win_rate = self.win_trades / self.total_trades if self.total_trades > 0 else 0
        
        return {
            'strategy_name': self.strategy_name,
            'active': self.active,
            'trading': self.trading,
            'total_trades': self.total_trades,
            'win_trades': self.win_trades,
            'lose_trades': self.lose_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'symbols': self.symbols,
            'parameters': self.get_parameters_info()
        }
        
    # 抽象方法，子类必须实现
    @abstractmethod
    def on_start(self):
        """策略启动时调用"""
        pass
        
    @abstractmethod
    def on_stop(self):
        """策略停止时调用"""
        pass
        
    @abstractmethod
    def on_tick_data(self, tick: TickData):
        """处理Tick数据"""
        pass
        
    @abstractmethod
    def on_bar_data(self, bar: BarData):
        """处理K线数据"""
        pass
        
    def on_order_update(self, order: OrderData):
        """处理订单更新（可选实现）"""
        pass
        
    def on_trade_update(self, trade: TradeData):
        """处理成交更新（可选实现）"""
        pass
