#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测模块测试

测试回测引擎、分析器、报告生成器等功能。
"""

import sys
import unittest
import tempfile
import os
from pathlib import Path
from datetime import datetime, date, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.backtest.engine import BacktestEngine
from src.backtest.analyzer import BacktestAnalyzer
from src.backtest.reporter import BacktestReporter


class MockStrategy:
    """模拟策略类"""
    
    def __init__(self, name: str):
        self.name = name
        self.bars_count = 0
        
    def on_bar(self, dt: datetime):
        """处理K线数据"""
        self.bars_count += 1
        # 简单的买入持有策略
        if self.bars_count == 1:
            # 第一根K线买入
            pass


class TestBacktestEngine(unittest.TestCase):
    """回测引擎测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        self.backtest_engine = BacktestEngine(self.main_engine, self.main_engine.event_engine)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'backtest_engine'):
            self.backtest_engine.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.backtest_engine)
        self.assertEqual(self.backtest_engine.initial_capital, 1000000.0)
        self.assertEqual(self.backtest_engine.mode, 'bar')
        
    def test_start_stop(self):
        """测试启动停止"""
        self.backtest_engine.start()
        self.assertTrue(self.backtest_engine.active)
        
        # 检查交易模块初始化
        self.assertIsNotNone(self.backtest_engine.trader)
        self.assertIsNotNone(self.backtest_engine.portfolio)
        self.assertIsNotNone(self.backtest_engine.risk_manager)
        
        self.backtest_engine.stop()
        self.assertFalse(self.backtest_engine.is_running)
        
    def test_set_parameters(self):
        """测试设置参数"""
        start_date = date(2023, 1, 1)
        end_date = date(2023, 12, 31)
        initial_capital = 500000.0
        
        self.backtest_engine.set_parameters(
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital
        )
        
        self.assertEqual(self.backtest_engine.start_date, start_date)
        self.assertEqual(self.backtest_engine.end_date, end_date)
        self.assertEqual(self.backtest_engine.initial_capital, initial_capital)
        
    def test_load_data(self):
        """测试加载数据"""
        # 设置回测参数
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 31)
        self.backtest_engine.set_parameters(start_date, end_date)
        
        # 加载数据（使用模拟数据）
        symbols = ["000001.SZ", "600000.SH"]
        self.backtest_engine.load_data(symbols)
        
        # 检查数据加载
        self.assertEqual(len(self.backtest_engine.historical_data), 2)
        self.assertIn("000001.SZ", self.backtest_engine.historical_data)
        self.assertIn("600000.SH", self.backtest_engine.historical_data)
        
        # 检查数据内容
        df = self.backtest_engine.historical_data["000001.SZ"]
        self.assertFalse(df.empty)
        self.assertIn('open', df.columns)
        self.assertIn('high', df.columns)
        self.assertIn('low', df.columns)
        self.assertIn('close', df.columns)
        self.assertIn('volume', df.columns)
        
    def test_add_strategy(self):
        """测试添加策略"""
        strategy = MockStrategy("TestStrategy")
        self.backtest_engine.add_strategy("test", strategy)
        
        self.assertIn("test", self.backtest_engine.strategies)
        self.assertEqual(self.backtest_engine.strategies["test"], strategy)
        
    def test_generate_mock_data(self):
        """测试生成模拟数据"""
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 10)
        self.backtest_engine.start_date = start_date
        self.backtest_engine.end_date = end_date
        
        df = self.backtest_engine._generate_mock_data("TEST.SZ")
        
        self.assertFalse(df.empty)
        self.assertEqual(len(df), 10)  # 10天数据
        
        # 检查价格合理性
        self.assertTrue(all(df['high'] >= df['low']))
        self.assertTrue(all(df['high'] >= df['open']))
        self.assertTrue(all(df['high'] >= df['close']))
        self.assertTrue(all(df['low'] <= df['open']))
        self.assertTrue(all(df['low'] <= df['close']))
        
    def test_run_backtest_simple(self):
        """测试简单回测"""
        # 设置回测参数
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 10)
        self.backtest_engine.set_parameters(start_date, end_date)
        
        # 加载数据
        symbols = ["000001.SZ"]
        self.backtest_engine.load_data(symbols)
        
        # 添加策略
        strategy = MockStrategy("TestStrategy")
        self.backtest_engine.add_strategy("test", strategy)
        
        # 启动回测引擎
        self.backtest_engine.start()
        
        # 运行回测
        results = self.backtest_engine.run_backtest()
        
        # 检查结果
        self.assertIsInstance(results, dict)
        self.assertIn('basic_info', results)
        self.assertIn('performance', results)
        self.assertIn('daily_results', results)
        
        # 检查基础信息
        basic_info = results['basic_info']
        self.assertEqual(basic_info['start_date'], start_date)
        self.assertEqual(basic_info['end_date'], end_date)
        self.assertEqual(basic_info['initial_capital'], 1000000.0)
        
        # 检查策略执行
        self.assertGreater(strategy.bars_count, 0)


class TestBacktestAnalyzer(unittest.TestCase):
    """回测分析器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = BacktestAnalyzer()
        
        # 创建模拟回测结果
        self.mock_results = {
            'basic_info': {
                'start_date': date(2023, 1, 1),
                'end_date': date(2023, 12, 31),
                'initial_capital': 1000000.0,
                'final_capital': 1200000.0,
                'total_return': 0.2,
                'total_trades': 100,
                'win_rate': 0.6
            },
            'daily_results': [
                {'date': date(2023, 1, 1), 'capital': 1000000.0, 'pnl': 0.0},
                {'date': date(2023, 1, 2), 'capital': 1010000.0, 'pnl': 10000.0},
                {'date': date(2023, 1, 3), 'capital': 1005000.0, 'pnl': 5000.0},
                {'date': date(2023, 1, 4), 'capital': 1020000.0, 'pnl': 20000.0},
                {'date': date(2023, 1, 5), 'capital': 1015000.0, 'pnl': 15000.0},
            ],
            'trades': []
        }
        
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.analyzer)
        self.assertIsNone(self.analyzer.results)
        self.assertIsNone(self.analyzer.daily_df)
        
    def test_load_results(self):
        """测试加载结果"""
        self.analyzer.load_results(self.mock_results)
        
        self.assertIsNotNone(self.analyzer.results)
        self.assertIsNotNone(self.analyzer.daily_df)
        
        # 检查DataFrame
        self.assertEqual(len(self.analyzer.daily_df), 5)
        self.assertIn('capital', self.analyzer.daily_df.columns)
        self.assertIn('pnl', self.analyzer.daily_df.columns)
        
    def test_calculate_detailed_metrics(self):
        """测试计算详细指标"""
        self.analyzer.load_results(self.mock_results)
        metrics = self.analyzer.calculate_detailed_metrics()
        
        self.assertIsInstance(metrics, dict)
        self.assertIn('basic_info', metrics)
        self.assertIn('return_metrics', metrics)
        self.assertIn('risk_metrics', metrics)
        self.assertIn('risk_adjusted_metrics', metrics)
        
        # 检查收益指标
        return_metrics = metrics['return_metrics']
        self.assertIn('total_return', return_metrics)
        self.assertIn('annual_return', return_metrics)
        
        # 检查风险指标
        risk_metrics = metrics['risk_metrics']
        self.assertIn('volatility', risk_metrics)
        self.assertIn('max_drawdown', risk_metrics)
        
    def test_generate_report(self):
        """测试生成报告"""
        self.analyzer.load_results(self.mock_results)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            report_path = f.name
            
        try:
            report_content = self.analyzer.generate_report(report_path)
            
            self.assertIsInstance(report_content, str)
            self.assertIn("回测分析报告", report_content)
            self.assertIn("基础信息", report_content)
            self.assertIn("收益指标", report_content)
            
            # 检查文件是否生成
            self.assertTrue(os.path.exists(report_path))
            
        finally:
            if os.path.exists(report_path):
                os.remove(report_path)


class TestBacktestReporter(unittest.TestCase):
    """回测报告生成器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.reporter = BacktestReporter()
        
        # 创建模拟指标数据
        self.mock_metrics = {
            'basic_info': {
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'initial_capital': 1000000.0,
                'final_capital': 1200000.0,
                'total_trades': 100
            },
            'return_metrics': {
                'total_return': 0.2,
                'annual_return': 0.2,
                'monthly_return': 0.015
            },
            'risk_metrics': {
                'volatility': 0.15,
                'max_drawdown': 0.08,
                'max_drawdown_duration': 15,
                'var_95': -0.02
            },
            'risk_adjusted_metrics': {
                'sharpe_ratio': 1.2,
                'sortino_ratio': 1.5,
                'calmar_ratio': 2.5
            },
            'trade_metrics': {
                'total_trades': 100,
                'avg_trades_per_day': 0.4
            },
            'win_loss_metrics': {
                'win_rate': 0.6,
                'win_trades': 60,
                'lose_trades': 40
            }
        }
        
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.reporter)
        self.assertTrue(self.reporter.template_dir.exists())
        
    def test_generate_summary_report(self):
        """测试生成摘要报告"""
        summary = self.reporter.generate_summary_report(self.mock_metrics)
        
        self.assertIsInstance(summary, str)
        self.assertIn("回测结果摘要", summary)
        self.assertIn("总收益率", summary)
        self.assertIn("年化收益率", summary)
        self.assertIn("最大回撤", summary)
        self.assertIn("策略评级", summary)
        
    def test_calculate_strategy_rating(self):
        """测试策略评级计算"""
        rating = self.reporter._calculate_strategy_rating(self.mock_metrics)
        
        self.assertIsInstance(rating, str)
        self.assertIn("(", rating)  # 评级格式包含括号
        
    def test_generate_comparison_report(self):
        """测试生成对比报告"""
        results_list = [self.mock_metrics, self.mock_metrics]
        strategy_names = ["策略A", "策略B"]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            report_path = f.name
            
        try:
            output_path = self.reporter.generate_comparison_report(
                results_list, strategy_names, report_path
            )
            
            self.assertEqual(output_path, report_path)
            self.assertTrue(os.path.exists(report_path))
            
            # 检查报告内容
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.assertIn("策略对比分析报告", content)
            self.assertIn("策略A", content)
            self.assertIn("策略B", content)
            self.assertIn("推荐策略", content)
            
        finally:
            if os.path.exists(report_path):
                os.remove(report_path)


class TestBacktestIntegration(unittest.TestCase):
    """回测模块集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.main_engine = MainEngine()
        self.main_engine.start()
        
        self.backtest_engine = BacktestEngine(self.main_engine, self.main_engine.event_engine)
        self.analyzer = BacktestAnalyzer()
        self.reporter = BacktestReporter()
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'backtest_engine'):
            self.backtest_engine.stop()
        if hasattr(self, 'main_engine'):
            self.main_engine.stop()
            
    def test_complete_backtest_workflow(self):
        """测试完整回测流程"""
        # 1. 设置回测参数
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 10)
        self.backtest_engine.set_parameters(start_date, end_date)
        
        # 2. 加载数据
        symbols = ["000001.SZ"]
        self.backtest_engine.load_data(symbols)
        
        # 3. 添加策略
        strategy = MockStrategy("TestStrategy")
        self.backtest_engine.add_strategy("test", strategy)
        
        # 4. 启动并运行回测
        self.backtest_engine.start()
        results = self.backtest_engine.run_backtest()
        
        # 5. 分析结果
        self.analyzer.load_results(results)
        detailed_metrics = self.analyzer.calculate_detailed_metrics()
        
        # 6. 生成报告
        summary_report = self.reporter.generate_summary_report(detailed_metrics)
        
        # 验证结果
        self.assertIsInstance(results, dict)
        self.assertIsInstance(detailed_metrics, dict)
        self.assertIsInstance(summary_report, str)
        
        # 检查关键指标存在
        self.assertIn('basic_info', detailed_metrics)
        self.assertIn('return_metrics', detailed_metrics)
        self.assertIn('risk_metrics', detailed_metrics)
        
        # 检查报告内容
        self.assertIn("回测结果摘要", summary_report)
        self.assertIn("策略评级", summary_report)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
