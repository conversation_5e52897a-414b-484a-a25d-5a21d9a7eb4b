# 数据配置文件
data:
  # 数据源配置
  sources:
    # miniqmt数据源
    miniqmt:
      enabled: true
      host: "127.0.0.1"
      port: 58610
      username: ""
      password: ""
      timeout: 30
      retry_times: 3
      retry_interval: 5
      
    # 备用数据源
    backup:
      enabled: false
      type: "tushare"  # tushare, akshare, yfinance
      token: ""
      
  # 数据存储配置
  storage:
    # 本地存储
    local:
      enabled: true
      base_path: "data/market_data"
      format: "hdf5"  # hdf5, csv, parquet
      compression: "gzip"
      
    # 云存储（可选）
    cloud:
      enabled: false
      type: "s3"  # s3, oss, cos
      bucket: ""
      access_key: ""
      secret_key: ""
      
  # 数据类型配置
  data_types:
    # K线数据
    kline:
      enabled: true
      frequencies: ["1m", "5m", "15m", "30m", "1h", "1d"]
      fields: ["open", "high", "low", "close", "volume", "amount"]
      
    # Tick数据
    tick:
      enabled: true
      fields: ["time", "last_price", "volume", "amount", "bid_price", "ask_price", "bid_volume", "ask_volume"]
      
    # Level2数据
    level2:
      enabled: false
      fields: ["time", "bid_prices", "ask_prices", "bid_volumes", "ask_volumes"]
      
  # 数据更新配置
  update:
    # 实时数据更新
    realtime:
      enabled: true
      buffer_size: 1000
      flush_interval: 60  # 秒
      
    # 历史数据更新
    historical:
      enabled: true
      update_time: "18:00:00"  # 每日更新时间
      lookback_days: 30        # 回望天数
      
  # 数据清理配置
  cleanup:
    enabled: true
    retention_days: 365      # 数据保留天数
    cleanup_time: "02:00:00" # 清理时间
    
# 行情订阅配置
subscription:
  # 股票订阅
  stocks:
    enabled: true
    symbols: []  # 空表示订阅所有，或指定具体股票代码
    data_types: ["kline_1m", "tick"]
    
  # 期货订阅
  futures:
    enabled: true
    symbols: []
    data_types: ["kline_1m", "tick"]
    
  # 指数订阅
  indices:
    enabled: true
    symbols: ["000001.SH", "399001.SZ", "399006.SZ"]  # 上证指数、深证成指、创业板指
    data_types: ["kline_1m"]
    
# 数据质量控制
quality_control:
  # 数据验证
  validation:
    enabled: true
    check_missing: true      # 检查缺失数据
    check_duplicates: true   # 检查重复数据
    check_outliers: true     # 检查异常值
    
  # 数据修复
  repair:
    enabled: true
    fill_method: "forward"   # forward, backward, interpolate
    outlier_method: "clip"   # clip, remove, interpolate
    
# 数据缓存配置
cache:
  # 内存缓存
  memory:
    enabled: true
    max_size: "1GB"
    ttl: 3600  # 生存时间（秒）
    
  # 磁盘缓存
  disk:
    enabled: true
    path: "data/cache"
    max_size: "10GB"
    
# 数据接口配置
api:
  # 数据查询接口
  query:
    max_records: 10000       # 单次查询最大记录数
    timeout: 30              # 查询超时时间
    
  # 数据推送接口
  push:
    enabled: true
    port: 8001
    max_connections: 100
    
# 数据监控配置
monitoring:
  enabled: true
  metrics:
    - "data_latency"         # 数据延迟
    - "data_completeness"    # 数据完整性
    - "update_frequency"     # 更新频率
    - "storage_usage"        # 存储使用量
    
  alerts:
    latency_threshold: 5.0   # 延迟阈值（秒）
    missing_threshold: 0.05  # 缺失数据阈值
    
# 数据备份配置
backup:
  enabled: true
  schedule: "0 3 * * *"      # Cron表达式，每天3点备份
  retention_days: 30         # 备份保留天数
  compression: true
  destination: "data/backup"
