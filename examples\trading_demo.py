#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易模块演示脚本

演示如何使用交易器进行下单、撤单、查询持仓等操作。
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.engine import MainEngine
from src.core.base import Direction, Offset, OrderType, OrderStatus
from src.trading.trader import Trader
from src.trading.portfolio import Portfolio
from src.trading.risk_manager import RiskManager
from loguru import logger


def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("交易模块演示开始")
    logger.info("=" * 50)
    
    # 创建主引擎
    main_engine = MainEngine()
    
    try:
        # 启动主引擎
        main_engine.start()
        
        # 创建交易模块
        trader = Trader(main_engine, main_engine.event_engine)
        portfolio = Portfolio(main_engine, main_engine.event_engine)
        risk_manager = RiskManager(main_engine, main_engine.event_engine)
        
        # 启动交易模块
        trader.start()
        portfolio.start()
        risk_manager.start()
        
        # 显示初始状态
        logger.info("初始状态:")
        accounts = trader.get_accounts()
        if accounts:
            account = accounts[0]
            logger.info(f"账户ID: {account.accountid}")
            logger.info(f"账户余额: {account.balance:,.2f}")
            logger.info(f"可用资金: {account.available:,.2f}")
            
        stats = portfolio.get_statistics()
        logger.info(f"初始资金: {stats['initial_capital']:,.2f}")
        logger.info(f"当前资金: {stats['current_capital']:,.2f}")
        
        risk_level = risk_manager.get_risk_level()
        logger.info(f"风险等级: {risk_level.value}")
        
        # 演示交易流程
        logger.info("\n" + "=" * 30)
        logger.info("开始交易演示")
        logger.info("=" * 30)
        
        # 1. 提交买入订单
        logger.info("1. 提交买入订单...")
        symbols = ["000001.SZ", "600000.SH", "000300.SH"]
        order_ids = []
        
        for i, symbol in enumerate(symbols):
            exchange = symbol.split('.')[1]
            price = 10.50 + i * 0.50  # 不同价格
            volume = 1000 + i * 500   # 不同数量
            
            order_id = trader.submit_order(
                symbol=symbol,
                exchange=exchange,
                direction=Direction.LONG,
                offset=Offset.OPEN,
                price=price,
                volume=volume,
                order_type=OrderType.LIMIT
            )
            
            if order_id:
                order_ids.append(order_id)
                logger.info(f"提交订单成功: {order_id} {symbol} {volume}@{price}")
            else:
                logger.warning(f"提交订单失败: {symbol}")
                
        # 等待订单处理
        logger.info("等待订单处理...")
        time.sleep(1.0)
        
        # 2. 查询订单状态
        logger.info("\n2. 查询订单状态...")
        for order_id in order_ids:
            order = trader.get_order(order_id)
            if order:
                logger.info(f"订单 {order_id}: {order.symbol} {order.status.value} "
                           f"成交:{order.traded}/{order.volume}")
                           
        # 3. 查询成交记录
        logger.info("\n3. 查询成交记录...")
        trades = trader.get_trades()
        for trade in trades:
            logger.info(f"成交: {trade.tradeid} {trade.symbol} {trade.direction.value} "
                       f"{trade.volume}@{trade.price} {trade.datetime}")
                       
        # 4. 查询持仓
        logger.info("\n4. 查询持仓...")
        positions = trader.get_positions()
        for position in positions:
            logger.info(f"持仓: {position.symbol} {position.direction.value} "
                       f"{position.volume}@{position.price} 盈亏:{position.pnl:.2f}")
                       
        # 5. 提交卖出订单
        logger.info("\n5. 提交卖出订单...")
        if positions:
            position = positions[0]  # 选择第一个持仓
            
            sell_order_id = trader.submit_order(
                symbol=position.symbol,
                exchange=position.exchange,
                direction=Direction.SHORT,
                offset=Offset.CLOSE,
                price=position.price + 0.20,  # 稍高价格卖出
                volume=position.volume // 2,  # 卖出一半
                order_type=OrderType.LIMIT
            )
            
            if sell_order_id:
                logger.info(f"提交卖出订单成功: {sell_order_id}")
                
                # 等待成交
                time.sleep(0.5)
                
                # 查询卖出订单状态
                sell_order = trader.get_order(sell_order_id)
                if sell_order:
                    logger.info(f"卖出订单状态: {sell_order.status.value}")
                    
        # 6. 演示撤单
        logger.info("\n6. 演示撤单...")
        
        # 提交一个不容易成交的订单
        cancel_order_id = trader.submit_order(
            symbol="000001.SZ",
            exchange="SZ",
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=1.00,  # 很低的价格，不会成交
            volume=100,
            order_type=OrderType.LIMIT
        )
        
        if cancel_order_id:
            logger.info(f"提交待撤销订单: {cancel_order_id}")
            
            # 立即撤销
            success = trader.cancel_order(cancel_order_id)
            if success:
                logger.info("撤销订单成功")
                
                # 检查订单状态
                cancelled_order = trader.get_order(cancel_order_id)
                if cancelled_order:
                    logger.info(f"撤销后订单状态: {cancelled_order.status.value}")
                    
        # 7. 显示最终统计
        logger.info("\n7. 最终统计...")
        
        # 账户信息
        accounts = trader.get_accounts()
        if accounts:
            account = accounts[0]
            logger.info(f"最终账户余额: {account.balance:,.2f}")
            logger.info(f"最终可用资金: {account.available:,.2f}")
            
        # 投资组合统计
        final_stats = portfolio.get_statistics()
        logger.info(f"总交易次数: {final_stats['total_trades']}")
        logger.info(f"总盈亏: {final_stats['total_pnl']:,.2f}")
        logger.info(f"总手续费: {final_stats['total_commission']:,.2f}")
        logger.info(f"总收益率: {final_stats['total_return']:.2%}")
        logger.info(f"胜率: {final_stats['win_rate']:.2%}")
        logger.info(f"持仓数量: {final_stats['position_count']}")
        
        # 风险管理信息
        final_risk_level = risk_manager.get_risk_level()
        logger.info(f"最终风险等级: {final_risk_level.value}")
        
        daily_trades_count = risk_manager.get_daily_trades_count()
        logger.info(f"今日交易次数: {daily_trades_count}")
        
        risk_alerts = risk_manager.get_risk_alerts()
        if risk_alerts:
            logger.info(f"风险警报数量: {len(risk_alerts)}")
            for alert in risk_alerts[-3:]:  # 显示最近3个警报
                logger.info(f"  {alert['timestamp']}: {alert['message']}")
        else:
            logger.info("无风险警报")
            
        # 8. 演示批量撤单
        logger.info("\n8. 演示批量撤单...")
        
        # 提交多个订单
        batch_order_ids = []
        for i in range(3):
            order_id = trader.submit_order(
                symbol=f"00000{i+2}.SZ",
                exchange="SZ",
                direction=Direction.LONG,
                offset=Offset.OPEN,
                price=5.00,  # 低价格，不会成交
                volume=100,
                order_type=OrderType.LIMIT
            )
            if order_id:
                batch_order_ids.append(order_id)
                
        logger.info(f"提交了 {len(batch_order_ids)} 个待撤销订单")
        
        # 批量撤销
        cancelled_count = trader.cancel_all_orders()
        logger.info(f"批量撤销了 {cancelled_count} 个订单")
        
        # 9. 显示订单历史
        logger.info("\n9. 订单历史...")
        all_orders = trader.get_orders()
        logger.info(f"总订单数量: {len(all_orders)}")
        
        # 按状态统计
        status_count = {}
        for order in all_orders:
            status = order.status.value
            status_count[status] = status_count.get(status, 0) + 1
            
        for status, count in status_count.items():
            logger.info(f"  {status}: {count}")
            
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
        
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        
    finally:
        # 清理资源
        logger.info("清理资源...")
        if 'risk_manager' in locals():
            risk_manager.stop()
        if 'portfolio' in locals():
            portfolio.stop()
        if 'trader' in locals():
            trader.stop()
        main_engine.stop()
        
    logger.info("=" * 50)
    logger.info("交易模块演示结束")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
